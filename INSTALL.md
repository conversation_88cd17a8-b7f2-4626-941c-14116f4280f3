# 安装和使用指南

## 🚀 快速开始

### 1. 环境准备

确保您的开发环境满足以下要求：

- **Node.js**: >= 18.0.0 LTS
- **VSCode**: >= 1.60.0
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+

### 2. 安装依赖

```bash
# 1. 克隆项目（如果从源码安装）
git clone <your-repository-url>
cd email-verification-helper

# 2. 安装插件核心依赖
npm install

# 3. 安装前端依赖
cd webview
npm install
cd ..
```

### 3. 编译项目

```bash
# 编译插件和前端
npm run compile
```

### 4. 调试运行

在VSCode中：
1. 打开项目文件夹
2. 按 `F5` 启动调试
3. 在新打开的VSCode窗口中测试插件

## 📋 使用说明

### 打开插件面板

有以下几种方式打开插件：

1. **命令面板**：
   - 按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (macOS)
   - 输入 "Email Helper: 打开邮箱助手"

2. **快捷命令**：
   - `Ctrl+Shift+E` 生成邮箱
   - `Ctrl+Shift+V` 获取验证码
   - `Ctrl+Shift+D` 清理数据

### 功能使用

#### 📧 邮箱生成器

1. **基础使用**：
   - 点击"生成邮箱"按钮
   - 自动生成临时邮箱地址
   - 点击"复制"按钮复制到剪贴板

2. **自定义配置**：
   - 选择不同的邮箱后缀
   - 调整用户名长度（4-20位）
   - 查看和管理历史记录

#### 🔢 验证码获取器

1. **设置监控邮箱**：
   - 输入要监控的邮箱地址
   - 配置关键词（如：验证码、code、verification）

2. **获取验证码**：
   - 点击"获取验证码"按钮
   - 系统自动提取最新验证码
   - 点击"复制"按钮复制验证码

3. **自动检查**：
   - 启用自动检查开关
   - 设置检查间隔（10-300秒）
   - 系统会定期检查新邮件

#### 🗑️ 数据清理器

1. **选择清理目标**：
   - ✅ 搜索历史：VSCode搜索和文件历史
   - ✅ 工作空间历史：工作空间存储数据
   - ✅ 扩展数据：扩展临时文件和缓存
   - ✅ 缓存文件：系统缓存和临时文件

2. **预览清理内容**：
   - 点击"预览清理内容"
   - 查看将要删除的文件大小
   - 确认清理范围

3. **执行清理**：
   - 点击"开始清理"
   - 确认清理操作
   - 查看清理结果统计

## ⚙️ 配置设置

### 打开设置

1. 点击插件界面右上角的设置按钮
2. 或通过VSCode设置搜索 "Email Helper"

### 配置选项

#### 邮箱生成设置
- **默认后缀**：选择或自定义邮箱后缀
- **用户名长度**：4-20位字符
- **历史记录**：保存1-100条记录

#### 验证码获取设置
- **检查间隔**：10-3600秒
- **提取关键词**：自定义关键词列表

#### 数据清理设置
- **清理确认**：是否显示确认对话框
- **清理预览**：是否显示预览信息

#### 高级设置
- **调试模式**：启用详细日志
- **配置导入/导出**：备份和恢复设置
- **重置设置**：恢复默认配置

## 🔧 故障排除

### 常见问题

#### 1. 插件无法启动
**症状**：点击命令没有反应
**解决方案**：
```bash
# 重新编译项目
npm run compile

# 检查依赖是否完整
npm install
cd webview && npm install
```

#### 2. 前端界面显示异常
**症状**：WebView显示空白或错误
**解决方案**：
```bash
# 重新编译前端
cd webview
npm run build
```

#### 3. 验证码获取失败
**症状**：提示"获取验证码失败"
**原因**：MVP版本使用模拟数据
**说明**：这是正常现象，实际版本需要配置真实邮箱服务

#### 4. 数据清理权限错误
**症状**：提示权限不足
**解决方案**：
- 以管理员身份运行VSCode
- 检查文件夹访问权限
- 确保VSCode有足够的系统权限

### 开发调试

#### 启用调试模式
1. 打开插件设置
2. 启用"调试模式"
3. 查看VSCode开发者控制台的详细日志

#### 查看日志
```bash
# VSCode开发者控制台
Help -> Toggle Developer Tools -> Console

# 或者按快捷键
Ctrl+Shift+I (Windows/Linux)
Cmd+Option+I (macOS)
```

#### 重置插件状态
```bash
# 清理编译输出
rm -rf out/
rm -rf webview/dist/

# 重新编译
npm run compile
```

## 📞 获取帮助

### 技术支持
- 查看 [README.md](./README.md) 了解详细功能
- 提交 Issue 报告问题
- 查看项目 Wiki 获取更多信息

### 社区资源
- GitHub Discussions：讨论功能和想法
- 示例项目：参考使用案例
- 开发文档：深入了解架构设计

### 联系方式
- 项目主页：[GitHub Repository]
- 问题反馈：[GitHub Issues]
- 功能建议：[GitHub Discussions]

---

**提示**：这是MVP版本，某些功能可能还在完善中。如果遇到问题，请查看控制台日志并及时反馈！
