import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

export interface CleanupOptions {
    userConfig: boolean;
    extensionData: boolean;
    workspaceHistory: boolean;
    searchHistory: boolean;
    cacheFiles: boolean;
}

export interface CleanupResult {
    success: boolean;
    filesDeleted: number;
    spaceFreed: number; // KB
    errors: string[];
    details: string[];
}

export class DataCleanupService {
    private readonly vscodeDataPaths: string[];

    constructor() {
        this.vscodeDataPaths = this.getVSCodeDataPaths();
    }

    /**
     * 执行数据清理
     */
    async cleanupData(options?: CleanupOptions): Promise<CleanupResult> {
        const defaultOptions: CleanupOptions = {
            userConfig: false,      // 默认不清理用户配置
            extensionData: true,    // 清理扩展数据
            workspaceHistory: true, // 清理工作空间历史
            searchHistory: true,    // 清理搜索历史
            cacheFiles: true        // 清理缓存文件
        };

        const cleanupOptions = { ...defaultOptions, ...options };
        const result: CleanupResult = {
            success: true,
            filesDeleted: 0,
            spaceFreed: 0,
            errors: [],
            details: []
        };

        try {
            // 清理搜索历史
            if (cleanupOptions.searchHistory) {
                await this.cleanupSearchHistory(result);
            }

            // 清理工作空间历史
            if (cleanupOptions.workspaceHistory) {
                await this.cleanupWorkspaceHistory(result);
            }

            // 清理扩展数据
            if (cleanupOptions.extensionData) {
                await this.cleanupExtensionData(result);
            }

            // 清理缓存文件
            if (cleanupOptions.cacheFiles) {
                await this.cleanupCacheFiles(result);
            }

            // 清理用户配置（谨慎操作）
            if (cleanupOptions.userConfig) {
                await this.cleanupUserConfig(result);
            }

            result.success = result.errors.length === 0;
        } catch (error) {
            result.success = false;
            result.errors.push(`清理过程中发生错误: ${error}`);
        }

        return result;
    }

    /**
     * 获取可清理的数据预览
     */
    async getCleanupPreview(): Promise<{
        searchHistory: number;
        workspaceHistory: number;
        extensionData: number;
        cacheFiles: number;
        totalSize: number;
    }> {
        const preview = {
            searchHistory: 0,
            workspaceHistory: 0,
            extensionData: 0,
            cacheFiles: 0,
            totalSize: 0
        };

        try {
            // 计算各类数据的大小
            for (const dataPath of this.vscodeDataPaths) {
                if (fs.existsSync(dataPath)) {
                    // 搜索历史
                    const searchHistoryPath = path.join(dataPath, 'User', 'History');
                    if (fs.existsSync(searchHistoryPath)) {
                        preview.searchHistory += await this.calculateDirectorySize(searchHistoryPath);
                    }

                    // 工作空间历史
                    const workspaceStoragePath = path.join(dataPath, 'User', 'workspaceStorage');
                    if (fs.existsSync(workspaceStoragePath)) {
                        preview.workspaceHistory += await this.calculateDirectorySize(workspaceStoragePath);
                    }

                    // 扩展数据
                    const extensionsPath = path.join(dataPath, 'extensions');
                    if (fs.existsSync(extensionsPath)) {
                        preview.extensionData += await this.calculateDirectorySize(extensionsPath);
                    }

                    // 缓存文件
                    const cachePath = path.join(dataPath, 'CachedExtensions');
                    if (fs.existsSync(cachePath)) {
                        preview.cacheFiles += await this.calculateDirectorySize(cachePath);
                    }
                }
            }

            preview.totalSize = preview.searchHistory + preview.workspaceHistory + 
                              preview.extensionData + preview.cacheFiles;
        } catch (error) {
            console.error('计算预览数据时出错:', error);
        }

        return preview;
    }

    /**
     * 清理搜索历史
     */
    private async cleanupSearchHistory(result: CleanupResult): Promise<void> {
        for (const dataPath of this.vscodeDataPaths) {
            const historyPath = path.join(dataPath, 'User', 'History');
            if (fs.existsSync(historyPath)) {
                try {
                    const size = await this.calculateDirectorySize(historyPath);
                    await this.removeDirectory(historyPath);
                    result.filesDeleted += 1;
                    result.spaceFreed += size;
                    result.details.push(`清理搜索历史: ${historyPath}`);
                } catch (error) {
                    result.errors.push(`清理搜索历史失败: ${error}`);
                }
            }
        }
    }

    /**
     * 清理工作空间历史
     */
    private async cleanupWorkspaceHistory(result: CleanupResult): Promise<void> {
        for (const dataPath of this.vscodeDataPaths) {
            const workspaceStoragePath = path.join(dataPath, 'User', 'workspaceStorage');
            if (fs.existsSync(workspaceStoragePath)) {
                try {
                    const files = fs.readdirSync(workspaceStoragePath);
                    for (const file of files) {
                        const filePath = path.join(workspaceStoragePath, file);
                        const size = await this.calculateFileSize(filePath);
                        await this.removeFileOrDirectory(filePath);
                        result.filesDeleted += 1;
                        result.spaceFreed += size;
                    }
                    result.details.push(`清理工作空间历史: ${workspaceStoragePath}`);
                } catch (error) {
                    result.errors.push(`清理工作空间历史失败: ${error}`);
                }
            }
        }
    }

    /**
     * 清理扩展数据
     */
    private async cleanupExtensionData(result: CleanupResult): Promise<void> {
        for (const dataPath of this.vscodeDataPaths) {
            const extensionsPath = path.join(dataPath, 'extensions');
            if (fs.existsSync(extensionsPath)) {
                try {
                    // 只清理临时文件和缓存，不删除扩展本身
                    const tempDirs = ['logs', 'temp', 'cache'];
                    for (const tempDir of tempDirs) {
                        const tempPath = path.join(extensionsPath, tempDir);
                        if (fs.existsSync(tempPath)) {
                            const size = await this.calculateDirectorySize(tempPath);
                            await this.removeDirectory(tempPath);
                            result.filesDeleted += 1;
                            result.spaceFreed += size;
                        }
                    }
                    result.details.push(`清理扩展临时数据: ${extensionsPath}`);
                } catch (error) {
                    result.errors.push(`清理扩展数据失败: ${error}`);
                }
            }
        }
    }

    /**
     * 清理缓存文件
     */
    private async cleanupCacheFiles(result: CleanupResult): Promise<void> {
        for (const dataPath of this.vscodeDataPaths) {
            const cachePaths = [
                path.join(dataPath, 'CachedExtensions'),
                path.join(dataPath, 'logs'),
                path.join(dataPath, 'temp')
            ];

            for (const cachePath of cachePaths) {
                if (fs.existsSync(cachePath)) {
                    try {
                        const size = await this.calculateDirectorySize(cachePath);
                        await this.removeDirectory(cachePath);
                        result.filesDeleted += 1;
                        result.spaceFreed += size;
                        result.details.push(`清理缓存: ${cachePath}`);
                    } catch (error) {
                        result.errors.push(`清理缓存失败: ${error}`);
                    }
                }
            }
        }
    }

    /**
     * 清理用户配置（谨慎操作）
     */
    private async cleanupUserConfig(result: CleanupResult): Promise<void> {
        // 这个功能需要非常谨慎，可能会导致用户丢失重要配置
        // 在MVP版本中，我们只清理一些安全的配置文件
        result.details.push('用户配置清理功能暂未实现（安全考虑）');
    }

    /**
     * 获取VSCode数据路径
     */
    private getVSCodeDataPaths(): string[] {
        const platform = os.platform();
        const homeDir = os.homedir();
        const paths: string[] = [];

        switch (platform) {
            case 'win32':
                paths.push(path.join(homeDir, 'AppData', 'Roaming', 'Code'));
                paths.push(path.join(homeDir, 'AppData', 'Roaming', 'Code - Insiders'));
                break;
            case 'darwin':
                paths.push(path.join(homeDir, 'Library', 'Application Support', 'Code'));
                paths.push(path.join(homeDir, 'Library', 'Application Support', 'Code - Insiders'));
                break;
            case 'linux':
                paths.push(path.join(homeDir, '.config', 'Code'));
                paths.push(path.join(homeDir, '.config', 'Code - Insiders'));
                break;
        }

        return paths.filter(p => fs.existsSync(p));
    }

    /**
     * 计算目录大小
     */
    private async calculateDirectorySize(dirPath: string): Promise<number> {
        let totalSize = 0;
        try {
            const files = fs.readdirSync(dirPath);
            for (const file of files) {
                const filePath = path.join(dirPath, file);
                const stats = fs.statSync(filePath);
                if (stats.isDirectory()) {
                    totalSize += await this.calculateDirectorySize(filePath);
                } else {
                    totalSize += stats.size;
                }
            }
        } catch (error) {
            console.error(`计算目录大小失败: ${dirPath}`, error);
        }
        return Math.round(totalSize / 1024); // 转换为KB
    }

    /**
     * 计算文件大小
     */
    private async calculateFileSize(filePath: string): Promise<number> {
        try {
            const stats = fs.statSync(filePath);
            return Math.round(stats.size / 1024); // 转换为KB
        } catch (error) {
            return 0;
        }
    }

    /**
     * 删除目录
     */
    private async removeDirectory(dirPath: string): Promise<void> {
        if (fs.existsSync(dirPath)) {
            fs.rmSync(dirPath, { recursive: true, force: true });
        }
    }

    /**
     * 删除文件或目录
     */
    private async removeFileOrDirectory(filePath: string): Promise<void> {
        if (fs.existsSync(filePath)) {
            const stats = fs.statSync(filePath);
            if (stats.isDirectory()) {
                fs.rmSync(filePath, { recursive: true, force: true });
            } else {
                fs.unlinkSync(filePath);
            }
        }
    }
}
