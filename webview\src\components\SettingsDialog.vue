<template>
  <el-dialog
    v-model="visible"
    title="设置"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      size="default"
    >
      <el-tabs v-model="activeTab">
        <!-- 邮箱生成设置 -->
        <el-tab-pane label="邮箱生成" name="email">
          <el-form-item label="默认后缀" prop="defaultEmailSuffix">
            <el-select
              v-model="form.defaultEmailSuffix"
              filterable
              allow-create
              placeholder="选择或输入邮箱后缀"
              style="width: 100%"
            >
              <el-option
                v-for="suffix in emailSuffixes"
                :key="suffix"
                :label="suffix"
                :value="suffix"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="用户名长度" prop="usernameLength">
            <el-input-number
              v-model="form.usernameLength"
              :min="4"
              :max="20"
              :step="1"
              style="width: 100%"
            />
            <div class="form-tip">
              生成邮箱用户名的字符长度（4-20位）
            </div>
          </el-form-item>
          
          <el-form-item label="历史记录" prop="historyLimit">
            <el-input-number
              v-model="form.historyLimit"
              :min="1"
              :max="100"
              :step="1"
              style="width: 100%"
            />
            <div class="form-tip">
              保存的邮箱历史记录数量（1-100条）
            </div>
          </el-form-item>
        </el-tab-pane>

        <!-- 验证码获取设置 -->
        <el-tab-pane label="验证码获取" name="verification">
          <el-form-item label="检查间隔" prop="checkInterval">
            <el-input-number
              v-model="form.checkInterval"
              :min="10"
              :max="3600"
              :step="10"
              style="width: 100%"
            />
            <div class="form-tip">
              自动检查邮件的时间间隔（10-3600秒）
            </div>
          </el-form-item>
          
          <el-form-item label="提取关键词" prop="extractionKeywords">
            <el-select
              v-model="form.extractionKeywords"
              multiple
              filterable
              allow-create
              placeholder="添加验证码邮件关键词"
              style="width: 100%"
            >
              <el-option
                v-for="keyword in defaultKeywords"
                :key="keyword"
                :label="keyword"
                :value="keyword"
              />
            </el-select>
            <div class="form-tip">
              用于识别验证码邮件的关键词，支持中英文
            </div>
          </el-form-item>
        </el-tab-pane>

        <!-- 数据清理设置 -->
        <el-tab-pane label="数据清理" name="cleanup">
          <el-form-item label="清理确认">
            <el-switch
              v-model="form.cleanupConfirm"
              active-text="显示确认对话框"
              inactive-text="直接清理"
            />
            <div class="form-tip">
              清理前是否显示确认对话框
            </div>
          </el-form-item>
          
          <el-form-item label="清理预览">
            <el-switch
              v-model="form.showPreview"
              active-text="显示预览信息"
              inactive-text="不显示预览"
            />
            <div class="form-tip">
              清理前是否显示预览信息
            </div>
          </el-form-item>
          
          <el-form-item label="备份数据">
            <el-switch
              v-model="form.backupBeforeCleanup"
              active-text="清理前备份"
              inactive-text="不备份"
              disabled
            />
            <div class="form-tip">
              清理前备份重要数据（暂未实现）
            </div>
          </el-form-item>
        </el-tab-pane>

        <!-- 高级设置 -->
        <el-tab-pane label="高级" name="advanced">
          <el-form-item label="调试模式">
            <el-switch
              v-model="form.debugMode"
              active-text="启用调试"
              inactive-text="关闭调试"
            />
            <div class="form-tip">
              启用后会在控制台输出详细日志
            </div>
          </el-form-item>
          
          <el-form-item label="配置导出">
            <el-button @click="exportConfig" :icon="Download">
              导出配置
            </el-button>
            <div class="form-tip">
              导出当前配置为JSON文件
            </div>
          </el-form-item>
          
          <el-form-item label="配置导入">
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :show-file-list="false"
              accept=".json"
              :on-change="importConfig"
            >
              <el-button :icon="Upload">选择配置文件</el-button>
            </el-upload>
            <div class="form-tip">
              导入之前导出的配置文件
            </div>
          </el-form-item>
          
          <el-form-item label="重置设置">
            <el-button type="danger" @click="resetToDefault" :icon="RefreshLeft">
              恢复默认设置
            </el-button>
            <div class="form-tip">
              将所有设置恢复为默认值
            </div>
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="saveSettings" :loading="saving">
          保存设置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Upload, RefreshLeft } from '@element-plus/icons-vue'
import { useAppStore } from '@/stores/app'

// Props
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 状态管理
const appStore = useAppStore()

// 响应式数据
const visible = ref(false)
const saving = ref(false)
const activeTab = ref('email')
const formRef = ref()
const uploadRef = ref()

// 表单数据
const form = reactive({
  defaultEmailSuffix: '@temp-mail.org',
  usernameLength: 8,
  historyLimit: 10,
  checkInterval: 30,
  extractionKeywords: ['验证码', 'verification', 'code'],
  cleanupConfirm: true,
  showPreview: true,
  backupBeforeCleanup: false,
  debugMode: false
})

// 邮箱后缀选项
const emailSuffixes = ref([
  '@temp-mail.org',
  '@10minutemail.com',
  '@guerrillamail.com',
  '@mailinator.com',
  '@yopmail.com'
])

// 默认关键词
const defaultKeywords = ref([
  '验证码',
  'verification',
  'code',
  '验证',
  'verify',
  'OTP',
  '动态密码',
  'security code',
  'auth code'
])

// 表单验证规则
const rules = {
  defaultEmailSuffix: [
    { required: true, message: '请输入邮箱后缀', trigger: 'blur' },
    { pattern: /^@.+\..+$/, message: '邮箱后缀格式不正确', trigger: 'blur' }
  ],
  usernameLength: [
    { required: true, message: '请输入用户名长度', trigger: 'blur' },
    { type: 'number', min: 4, max: 20, message: '用户名长度必须在4-20之间', trigger: 'blur' }
  ],
  historyLimit: [
    { required: true, message: '请输入历史记录限制', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '历史记录限制必须在1-100之间', trigger: 'blur' }
  ],
  checkInterval: [
    { required: true, message: '请输入检查间隔', trigger: 'blur' },
    { type: 'number', min: 10, max: 3600, message: '检查间隔必须在10-3600秒之间', trigger: 'blur' }
  ],
  extractionKeywords: [
    { required: true, message: '请至少添加一个关键词', trigger: 'change' }
  ]
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  visible.value = newValue
  if (newValue) {
    loadSettings()
  }
})

// 监听visible变化
watch(visible, (newValue) => {
  emit('update:modelValue', newValue)
})

// 方法
const loadSettings = () => {
  if (appStore.config) {
    Object.assign(form, {
      defaultEmailSuffix: appStore.config.defaultEmailSuffix,
      usernameLength: appStore.config.usernameLength,
      historyLimit: appStore.config.historyLimit,
      checkInterval: appStore.config.checkInterval,
      extractionKeywords: [...appStore.config.extractionKeywords]
    })
  }

  // 从本地存储加载其他设置
  const localSettings = localStorage.getItem('appSettings')
  if (localSettings) {
    try {
      const settings = JSON.parse(localSettings)
      Object.assign(form, settings)
    } catch (error) {
      console.error('加载本地设置失败:', error)
    }
  }
}

const saveSettings = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
  } catch (error) {
    ElMessage.error('请检查输入内容')
    return
  }

  saving.value = true
  try {
    // 保存到应用配置
    await appStore.updateConfig({
      defaultEmailSuffix: form.defaultEmailSuffix,
      usernameLength: form.usernameLength,
      historyLimit: form.historyLimit,
      checkInterval: form.checkInterval,
      extractionKeywords: form.extractionKeywords
    })

    // 保存其他设置到本地存储
    const localSettings = {
      cleanupConfirm: form.cleanupConfirm,
      showPreview: form.showPreview,
      backupBeforeCleanup: form.backupBeforeCleanup,
      debugMode: form.debugMode
    }
    localStorage.setItem('appSettings', JSON.stringify(localSettings))

    ElMessage.success('设置保存成功')
    visible.value = false
  } catch (error) {
    ElMessage.error('设置保存失败: ' + (error instanceof Error ? error.message : '未知错误'))
  } finally {
    saving.value = false
  }
}

const handleClose = () => {
  visible.value = false
}

const exportConfig = () => {
  const config = {
    ...form,
    exportTime: new Date().toISOString(),
    version: '1.0.0'
  }

  const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `email-helper-config-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  ElMessage.success('配置导出成功')
}

const importConfig = (file: any) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const config = JSON.parse(e.target?.result as string)
      
      // 验证配置格式
      if (!config.defaultEmailSuffix || !config.extractionKeywords) {
        throw new Error('配置文件格式不正确')
      }

      Object.assign(form, config)
      ElMessage.success('配置导入成功')
    } catch (error) {
      ElMessage.error('配置文件格式错误: ' + (error instanceof Error ? error.message : '未知错误'))
    }
  }
  reader.readAsText(file.raw)
}

const resetToDefault = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要恢复默认设置吗？当前设置将会丢失。',
      '确认重置',
      {
        confirmButtonText: '确定重置',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 重置为默认值
    Object.assign(form, {
      defaultEmailSuffix: '@temp-mail.org',
      usernameLength: 8,
      historyLimit: 10,
      checkInterval: 30,
      extractionKeywords: ['验证码', 'verification', 'code'],
      cleanupConfirm: true,
      showPreview: true,
      backupBeforeCleanup: false,
      debugMode: false
    })

    ElMessage.success('已恢复默认设置')
  } catch (error) {
    // 用户取消
  }
}
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
  line-height: 1.4;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-tabs__content) {
  padding-top: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin: 5vh auto !important;
  }
  
  :deep(.el-form-item__label) {
    width: 100px !important;
  }
}
</style>
