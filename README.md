# Email Verification Helper

VSCode邮箱验证码助手插件 - 自动化邮箱生成、验证码获取和数据清理的开发者效率工具。

## 🚀 功能特性

### 📧 邮箱生成器
- **一键生成**：快速生成指定后缀的临时邮箱地址
- **多种后缀**：支持多种临时邮箱服务
- **历史记录**：保存最近生成的邮箱列表
- **快速复制**：一键复制邮箱到剪贴板

### 🔢 验证码获取器
- **智能提取**：自动从邮件中提取验证码
- **关键词过滤**：支持自定义关键词筛选
- **自动检查**：可设置自动检查新邮件
- **多格式支持**：支持数字、字母数字等多种验证码格式

### 🗑️ 数据清理器
- **选择性清理**：可选择要清理的数据类型
- **安全预览**：清理前显示预览信息
- **详细统计**：显示清理结果和释放空间
- **安全保护**：多重确认防止误操作

## 📦 安装使用

### 开发环境要求
- Node.js >= 18.0.0
- VSCode >= 1.60.0

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd email-verification-helper
```

2. **安装依赖**
```bash
# 安装插件依赖
npm install

# 安装前端依赖
cd webview
npm install
cd ..
```

3. **编译项目**
```bash
npm run compile
```

4. **调试运行**
- 在VSCode中按 `F5` 启动调试
- 或者使用命令面板运行 "Email Helper: 打开邮箱助手"

## 🛠️ 开发指南

### 项目结构
```
email-verification-helper/
├── src/                    # 插件核心代码
│   ├── extension.ts       # 插件入口
│   ├── panels/           # WebView面板
│   └── services/         # 业务服务
├── webview/              # Vue3前端应用
│   ├── src/
│   │   ├── components/   # Vue组件
│   │   ├── stores/       # Pinia状态管理
│   │   └── utils/        # 工具函数
│   └── dist/            # 构建输出
├── out/                  # 插件编译输出
└── package.json         # 插件配置
```

### 技术栈
- **插件核心**：TypeScript + VSCode Extension API
- **前端界面**：Vue 3 + Composition API + TypeScript
- **UI组件**：Element Plus
- **状态管理**：Pinia
- **构建工具**：Vite + Webpack

### 开发命令
```bash
# 编译插件
npm run compile

# 监听模式编译
npm run watch

# 编译前端
npm run compile:webview

# 监听模式编译前端
npm run watch:webview

# 代码检查
npm run lint

# 运行测试
npm test

# 打包发布
npm run package
```

## ⚙️ 配置说明

### 插件配置
在VSCode设置中可以配置以下选项：

- `emailHelper.defaultEmailSuffix`：默认邮箱后缀
- `emailHelper.usernameLength`：生成邮箱用户名长度
- `emailHelper.historyLimit`：历史记录保存数量
- `emailHelper.checkInterval`：邮件检查间隔（秒）
- `emailHelper.extractionKeywords`：验证码邮件关键词

### 邮箱服务配置
支持配置多种临时邮箱服务：
- TempMail (@temp-mail.org)
- 10MinuteMail (@10minutemail.com)
- Guerrilla Mail (@guerrillamail.com)
- 自定义邮箱服务

## 🔒 安全说明

### 数据安全
- 所有数据仅存储在本地
- 敏感信息加密存储
- 不向外部服务器发送用户数据

### 隐私保护
- 生成的临时邮箱与个人邮箱隔离
- 清理操作不留痕迹
- 用户完全控制数据的生成和删除

### 使用建议
- 仅用于开发测试目的
- 不要用于重要账号注册
- 定期清理敏感数据

## 🐛 问题反馈

如果您在使用过程中遇到问题，请：

1. 检查VSCode版本是否符合要求
2. 查看开发者控制台的错误信息
3. 提交Issue时请包含：
   - VSCode版本
   - 插件版本
   - 错误信息
   - 复现步骤

## 📄 许可证

MIT License

## 🤝 贡献指南

欢迎提交Pull Request和Issue！

### 开发流程
1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

### 代码规范
- 使用TypeScript
- 遵循ESLint规则
- 添加适当的注释
- 编写单元测试

## 📚 更新日志

### v1.0.0 (MVP版本)
- ✅ 基础邮箱生成功能
- ✅ 简单验证码获取功能
- ✅ 基础数据清理功能
- ✅ Vue3前端界面
- ✅ 基础配置管理

### 计划功能
- 🔄 完整的邮件服务集成
- 🔄 高级验证码提取规则
- 🔄 批量操作功能
- 🔄 云端配置同步
- 🔄 插件生态集成

---

**注意**：这是MVP版本，部分功能可能还在完善中。如有问题请及时反馈！
