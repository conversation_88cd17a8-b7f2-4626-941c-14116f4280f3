import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { vscodeApi } from '@/utils/vscode-api'

export interface AppConfig {
  defaultEmailSuffix: string
  usernameLength: number
  historyLimit: number
  checkInterval: number
  extractionKeywords: string[]
}

export const useAppStore = defineStore('app', () => {
  // 状态
  const config = ref<AppConfig>()
  const loading = ref(false)
  const error = ref<string | null>(null)
  const initialized = ref(false)

  // 计算属性
  const isInitialized = computed(() => initialized.value && !!config.value)

  // 操作方法
  const initialize = async () => {
    if (initialized.value) return

    loading.value = true
    try {
      const configData = await vscodeApi.getConfig()
      config.value = configData
      initialized.value = true
      error.value = null
    } catch (err) {
      error.value = err instanceof Error ? err.message : '初始化失败'
      console.error('初始化失败:', err)
    } finally {
      loading.value = false
    }
  }

  const updateConfig = async (newConfig: Partial<AppConfig>) => {
    if (!config.value) return

    loading.value = true
    try {
      await vscodeApi.updateConfig(newConfig)
      config.value = { ...config.value, ...newConfig }
      error.value = null
    } catch (err) {
      error.value = err instanceof Error ? err.message : '配置更新失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  const setError = (errorMessage: string | null) => {
    error.value = errorMessage
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    config,
    loading,
    error,
    initialized,
    
    // 计算属性
    isInitialized,
    
    // 操作方法
    initialize,
    updateConfig,
    setLoading,
    setError,
    clearError
  }
})
