import * as crypto from 'crypto';
import { ConfigService } from './ConfigService';

export interface EmailHistory {
    email: string;
    createdAt: Date;
}

export interface VerificationCodeResult {
    code: string;
    email: string;
    subject: string;
    receivedAt: Date;
}

export class EmailService {
    private emailHistory: EmailHistory[] = [];
    private configService: ConfigService;

    constructor(configService: ConfigService) {
        this.configService = configService;
        this.loadEmailHistory();
    }

    /**
     * 生成随机邮箱地址
     */
    async generateEmail(): Promise<string> {
        const config = this.configService.getConfig();
        const username = this.generateRandomUsername(config.usernameLength);
        const email = `${username}${config.defaultEmailSuffix}`;

        // 保存到历史记录
        this.addToHistory(email);

        return email;
    }

    /**
     * 获取最新的验证码
     */
    async getLatestVerificationCode(email?: string): Promise<string | null> {
        try {
            // 这里是模拟实现，实际需要连接邮箱服务
            // 在MVP版本中，我们先返回模拟数据
            const mockCode = this.generateMockVerificationCode();
            
            // TODO: 实际实现需要：
            // 1. 连接IMAP服务器
            // 2. 搜索包含关键词的邮件
            // 3. 解析邮件内容提取验证码
            
            return mockCode;
        } catch (error) {
            console.error('获取验证码失败:', error);
            throw new Error('获取验证码失败');
        }
    }

    /**
     * 获取邮箱历史记录
     */
    getEmailHistory(): EmailHistory[] {
        return this.emailHistory.slice().reverse(); // 返回副本，最新的在前
    }

    /**
     * 清空邮箱历史记录
     */
    clearEmailHistory(): void {
        this.emailHistory = [];
        this.saveEmailHistory();
    }

    /**
     * 生成随机用户名
     */
    private generateRandomUsername(length: number): string {
        const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        
        // 确保第一个字符是字母
        result += chars.charAt(Math.floor(Math.random() * 26));
        
        // 生成剩余字符
        for (let i = 1; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        
        return result;
    }

    /**
     * 添加到历史记录
     */
    private addToHistory(email: string): void {
        const config = this.configService.getConfig();
        
        // 检查是否已存在
        const existingIndex = this.emailHistory.findIndex(item => item.email === email);
        if (existingIndex !== -1) {
            // 如果已存在，更新时间并移到最前面
            this.emailHistory.splice(existingIndex, 1);
        }

        // 添加到开头
        this.emailHistory.unshift({
            email,
            createdAt: new Date()
        });

        // 限制历史记录数量
        if (this.emailHistory.length > config.historyLimit) {
            this.emailHistory = this.emailHistory.slice(0, config.historyLimit);
        }

        this.saveEmailHistory();
    }

    /**
     * 加载邮箱历史记录
     */
    private loadEmailHistory(): void {
        try {
            // 从VSCode配置中加载历史记录
            // 这里使用内存存储，实际可以存储到VSCode的globalState
            this.emailHistory = [];
        } catch (error) {
            console.error('加载邮箱历史失败:', error);
            this.emailHistory = [];
        }
    }

    /**
     * 保存邮箱历史记录
     */
    private saveEmailHistory(): void {
        try {
            // 保存到VSCode配置
            // 实际实现中可以使用context.globalState.update()
            console.log('保存邮箱历史:', this.emailHistory);
        } catch (error) {
            console.error('保存邮箱历史失败:', error);
        }
    }

    /**
     * 生成模拟验证码（MVP版本使用）
     */
    private generateMockVerificationCode(): string {
        // 生成6位数字验证码
        return Math.floor(100000 + Math.random() * 900000).toString();
    }

    /**
     * 验证邮箱格式
     */
    private isValidEmail(email: string): boolean {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * 提取验证码的正则表达式
     */
    private extractVerificationCode(content: string): string | null {
        // 常见的验证码格式
        const patterns = [
            /\b\d{6}\b/,           // 6位数字
            /\b\d{4}\b/,           // 4位数字
            /\b[A-Z0-9]{6}\b/,     // 6位字母数字组合
            /验证码[：:]\s*(\d+)/,  // 中文格式
            /code[：:]\s*(\d+)/i,  // 英文格式
        ];

        for (const pattern of patterns) {
            const match = content.match(pattern);
            if (match) {
                return match[1] || match[0];
            }
        }

        return null;
    }
}
