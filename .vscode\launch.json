{"version": "0.2.0", "configurations": [{"name": "Run Extension", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}"], "outFiles": ["${workspaceFolder}/out/**/*.js"], "preLaunchTask": "${workspaceFolder}/npm: compile"}, {"name": "Debug WebView", "type": "node", "request": "launch", "program": "${workspaceFolder}/webview/node_modules/.bin/vite", "args": ["--mode", "development"], "cwd": "${workspaceFolder}/webview", "console": "integratedTerminal"}]}