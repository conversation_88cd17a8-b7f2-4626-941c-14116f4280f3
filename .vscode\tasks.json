{"version": "2.0.0", "tasks": [{"type": "npm", "script": "compile", "group": "build", "presentation": {"panel": "shared"}, "problemMatcher": "$tsc"}, {"type": "npm", "script": "watch", "group": {"kind": "build", "isDefault": true}, "presentation": {"panel": "shared"}, "problemMatcher": "$tsc-watch"}, {"label": "Build WebView", "type": "shell", "command": "npm", "args": ["run", "build"], "options": {"cwd": "${workspaceFolder}/webview"}, "group": "build", "presentation": {"panel": "shared"}}, {"label": "Watch WebView", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/webview"}, "group": "build", "presentation": {"panel": "shared"}, "isBackground": true}]}