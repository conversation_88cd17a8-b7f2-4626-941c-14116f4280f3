import * as vscode from 'vscode';
import { EmailService } from '../services/EmailService';
import { DataCleanupService } from '../services/DataCleanupService';
import { ConfigService } from '../services/ConfigService';

interface Services {
    emailService: EmailService;
    dataCleanupService: DataCleanupService;
    configService: ConfigService;
}

export class EmailHelperPanel {
    public static currentPanel: EmailHelperPanel | undefined;
    public static readonly viewType = 'emailHelper';

    private readonly _panel: vscode.WebviewPanel;
    private readonly _extensionUri: vscode.Uri;
    private _disposables: vscode.Disposable[] = [];
    private readonly _services: Services;

    public static createOrShow(extensionUri: vscode.Uri, services: Services) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        // 如果已经有面板打开，则显示它
        if (EmailHelperPanel.currentPanel) {
            EmailHelperPanel.currentPanel._panel.reveal(column);
            return;
        }

        // 创建新面板
        const panel = vscode.window.createWebviewPanel(
            EmailHelperPanel.viewType,
            'Email Verification Helper',
            column || vscode.ViewColumn.One,
            {
                enableScripts: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(extensionUri, 'out'),
                    vscode.Uri.joinPath(extensionUri, 'webview', 'dist')
                ]
            }
        );

        EmailHelperPanel.currentPanel = new EmailHelperPanel(panel, extensionUri, services);
    }

    private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri, services: Services) {
        this._panel = panel;
        this._extensionUri = extensionUri;
        this._services = services;

        // 设置初始HTML内容
        this._update();

        // 监听面板关闭事件
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

        // 处理来自webview的消息
        this._panel.webview.onDidReceiveMessage(
            async (message) => {
                await this._handleMessage(message);
            },
            null,
            this._disposables
        );
    }

    private async _handleMessage(message: any) {
        try {
            switch (message.command) {
                case 'generateEmail':
                    const email = await this._services.emailService.generateEmail();
                    this._panel.webview.postMessage({
                        command: 'emailGenerated',
                        data: { email }
                    });
                    break;

                case 'getVerificationCode':
                    const code = await this._services.emailService.getLatestVerificationCode(message.data?.email);
                    this._panel.webview.postMessage({
                        command: 'verificationCodeReceived',
                        data: { code }
                    });
                    break;

                case 'cleanupData':
                    const cleanupResult = await this._services.dataCleanupService.cleanupData(message.data?.options);
                    this._panel.webview.postMessage({
                        command: 'cleanupCompleted',
                        data: cleanupResult
                    });
                    break;

                case 'getConfig':
                    const config = this._services.configService.getConfig();
                    this._panel.webview.postMessage({
                        command: 'configReceived',
                        data: config
                    });
                    break;

                case 'updateConfig':
                    await this._services.configService.updateConfig(message.data);
                    this._panel.webview.postMessage({
                        command: 'configUpdated',
                        data: { success: true }
                    });
                    break;

                case 'copyToClipboard':
                    await vscode.env.clipboard.writeText(message.data?.text || '');
                    vscode.window.showInformationMessage('已复制到剪贴板');
                    break;

                default:
                    console.warn('未知命令:', message.command);
            }
        } catch (error) {
            console.error('处理消息时出错:', error);
            this._panel.webview.postMessage({
                command: 'error',
                data: { message: error instanceof Error ? error.message : '未知错误' }
            });
        }
    }

    public dispose() {
        EmailHelperPanel.currentPanel = undefined;

        // 清理资源
        this._panel.dispose();

        while (this._disposables.length) {
            const disposable = this._disposables.pop();
            if (disposable) {
                disposable.dispose();
            }
        }
    }

    private _update() {
        const webview = this._panel.webview;
        this._panel.webview.html = this._getHtmlForWebview(webview);
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        // 获取webview资源URI
        const scriptUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, 'webview', 'dist', 'assets', 'index.js')
        );
        const styleUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, 'webview', 'dist', 'assets', 'index.css')
        );

        // 生成nonce用于CSP
        const nonce = this._getNonce();

        return `<!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}';">
            <link href="${styleUri}" rel="stylesheet">
            <title>Email Verification Helper</title>
        </head>
        <body>
            <div id="app"></div>
            <script nonce="${nonce}" src="${scriptUri}"></script>
        </body>
        </html>`;
    }

    private _getNonce() {
        let text = '';
        const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        for (let i = 0; i < 32; i++) {
            text += possible.charAt(Math.floor(Math.random() * possible.length));
        }
        return text;
    }
}
