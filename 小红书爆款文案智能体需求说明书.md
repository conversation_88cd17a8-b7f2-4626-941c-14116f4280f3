# 小红书爆款文案智能体服务需求说明书

## 1. 产品概述

### 1.1 产品定位
- **产品名称**：小红书爆款文案智能体
- **产品定位**：专为小红书平台优化的AI文案生成工具
- **核心价值**：帮助用户快速创作符合小红书平台特点的爆款文案，提升内容质量和传播效果

### 1.2 目标用户
- **主要用户**：小红书博主、内容创作者、品牌运营人员
- **次要用户**：电商卖家、代运营公司、营销从业者
- **潜在用户**：新媒体工作者、自媒体创业者

### 1.3 市场分析
- **市场规模**：小红书月活用户超2亿，内容创作需求巨大
- **竞争现状**：现有AI写作工具多为通用型，缺乏小红书专业化
- **市场机会**：垂直化、专业化的文案生成工具存在空白

### 1.4 商业模式
- **免费版**：每日5次生成，基础模板，广告支持
- **会员版**：无限生成，高级模板，无广告（¥29.9/月）
- **专业版**：批量生成，数据分析，API接口（¥99/月）
- **企业版**：定制服务，专属客服，私有部署（¥999/月起）

## 2. 产品功能架构

### 2.1 H5前端应用功能

#### 2.1.1 文案生成核心功能
- **智能文案生成器**
  - 多种文案类型选择（种草、测评、教程、好物推荐、穿搭、美食等）
  - 行业分类精准匹配（美妆护肤、服饰搭配、美食饮品、旅游出行、数码科技、家居生活等）
  - 文案风格自定义（种草型、专业型、亲民型、高端型、搞笑型）
  - 关键词智能扩展和语义理解
  - 产品信息结构化输入（品牌、价格、特点、使用场景）

- **标题优化功能**
  - 爆款标题模板库（数字型、疑问型、对比型、情感型）
  - 标题吸引力评分系统
  - 点击率预测算法
  - A/B测试标题生成
  - 标题长度和格式优化

- **内容结构优化**
  - 开头吸引句生成
  - 正文逻辑结构规划
  - 结尾互动引导语
  - 段落排版优化
  - 关键信息突出显示

#### 2.1.2 智能辅助功能
- **话题标签推荐**
  - 热门话题实时更新
  - 精准标签智能匹配
  - 话题热度评分
  - 相关话题推荐
  - 自定义话题管理

- **互动元素生成**
  - 评论引导语设计
  - 收藏理由提示
  - 分享文案生成
  - 用户互动问题设计
  - 社群讨论话题

- **视觉元素建议**
  - Emoji表情推荐
  - 特殊符号使用
  - 文字排版建议
  - 配图方向指导
  - 色彩搭配建议

#### 2.1.3 内容管理功能
- **历史文案管理**
  - 生成历史记录
  - 文案分类整理
  - 收藏夹功能
  - 标签管理系统
  - 搜索和筛选

- **模板库系统**
  - 官方精选模板
  - 用户自定义模板
  - 爆款案例库
  - 模板评分系统
  - 模板使用统计

- **导出分享功能**
  - 一键复制文案
  - 多格式导出（文本、图片、PDF）
  - 社交平台分享
  - 二维码生成
  - 链接分享功能

#### 2.1.4 用户体验功能
- **个性化设置**
  - 常用行业快捷选择
  - 个人风格偏好设置
  - 常用关键词库
  - 生成历史分析
  - 个性化推荐

- **智能学习功能**
  - 用户行为学习
  - 偏好模式识别
  - 个性化优化建议
  - 使用习惯分析
  - 效果反馈收集

### 2.2 后台管理系统功能

#### 2.2.1 用户管理模块
- **用户账户管理**
  - 用户注册登录系统
  - 账户信息管理
  - 密码安全策略
  - 第三方登录集成（微信、QQ）
  - 账户注销和数据清理

- **会员权限管理**
  - 会员等级体系
  - 权限分配管理
  - 使用次数限制
  - 功能访问控制
  - 会员到期提醒

- **订阅付费管理**
  - 订阅计划管理
  - 支付接口集成
  - 订单管理系统
  - 退款处理流程
  - 发票开具功能

#### 2.2.2 内容管理模块
- **模板内容管理**
  - 文案模板库维护
  - 模板分类管理
  - 模板质量审核
  - 模板使用统计
  - 模板更新发布

- **AI模型管理**
  - 模型版本控制
  - 模型性能监控
  - 训练数据管理
  - 模型效果评估
  - 模型优化迭代

- **内容安全管理**
  - 敏感词库维护
  - 违规内容检测
  - 内容审核流程
  - 用户举报处理
  - 合规性检查

#### 2.2.3 数据分析模块
- **用户行为分析**
  - 用户活跃度统计
  - 功能使用频率
  - 用户路径分析
  - 留存率分析
  - 流失用户分析

- **业务数据分析**
  - 文案生成量统计
  - 模板使用排行
  - 热门话题趋势
  - 收入数据分析
  - 转化率统计

- **效果评估分析**
  - 文案质量评分
  - 用户满意度调查
  - 效果反馈收集
  - 改进建议统计
  - 竞品对比分析

#### 2.2.4 运营管理模块
- **内容运营管理**
  - 热门话题更新
  - 爆款案例收集
  - 写作技巧发布
  - 用户教程制作
  - 活动策划执行

- **用户运营管理**
  - 用户分群管理
  - 精准营销推送
  - 用户反馈处理
  - 社群运营管理
  - 客户服务支持

- **系统运维管理**
  - 系统性能监控
  - 服务器状态管理
  - 数据备份恢复
  - 安全防护管理
  - 日志分析系统

## 3. 技术架构设计

### 3.1 H5前端技术栈
- **核心框架**：Vue3 + Composition API + TypeScript
- **构建工具**：Vite + PWA插件
- **UI组件**：Vant 4（移动端UI库）
- **状态管理**：Pinia
- **路由管理**：Vue Router 4
- **HTTP客户端**：Axios
- **样式方案**：Tailwind CSS + PostCSS
- **移动端适配**：viewport + rem + flexible

### 3.2 后端技术栈
- **运行环境**：Node.js 18+
- **Web框架**：Express.js + TypeScript
- **数据库**：MongoDB + Redis
- **AI服务**：OpenAI API / 自训练模型
- **文件存储**：阿里云OSS / 腾讯云COS
- **消息队列**：Redis / RabbitMQ
- **缓存策略**：Redis + 内存缓存
- **监控日志**：Winston + ELK Stack

### 3.3 系统架构
- **部署架构**：Docker + Kubernetes
- **负载均衡**：Nginx + PM2
- **CDN加速**：阿里云CDN / 腾讯云CDN
- **安全防护**：HTTPS + WAF + 限流
- **备份策略**：数据库备份 + 文件备份
- **监控告警**：Prometheus + Grafana

## 4. 核心功能详细设计

### 4.1 AI文案生成算法
- **模型选择**：基于GPT-3.5/4.0微调的小红书专用模型
- **训练数据**：10万+小红书爆款文案数据集
- **生成策略**：模板+AI生成+规则优化的混合模式
- **质量控制**：多轮生成+质量评分+人工筛选
- **个性化**：用户行为学习+偏好适配

### 4.2 内容安全机制
- **敏感词过滤**：多级敏感词库+实时更新
- **违规检测**：AI内容审核+人工复审
- **版权保护**：原创性检测+相似度分析
- **合规检查**：平台规则适配+政策更新

### 4.3 用户体验优化
- **响应速度**：CDN加速+缓存策略+异步处理
- **移动适配**：响应式设计+触摸优化+手势支持
- **离线功能**：PWA支持+本地缓存+离线提示
- **无障碍设计**：语音输入+大字体+高对比度

## 5. 数据库设计

### 5.1 核心数据表
- **用户表（users）**：用户基本信息、会员状态、偏好设置
- **文案表（contents）**：生成的文案内容、分类、标签
- **模板表（templates）**：文案模板、使用统计、评分
- **订单表（orders）**：付费订单、支付状态、订阅信息
- **日志表（logs）**：用户行为、系统操作、错误记录

### 5.2 缓存设计
- **用户缓存**：登录状态、个人设置、使用记录
- **内容缓存**：热门模板、常用文案、话题数据
- **系统缓存**：配置信息、统计数据、临时文件

## 6. 安全与合规

### 6.1 数据安全
- **数据加密**：传输加密（HTTPS）+ 存储加密
- **访问控制**：身份认证 + 权限管理 + API限流
- **隐私保护**：数据脱敏 + 最小化收集 + 用户授权
- **备份恢复**：定期备份 + 灾难恢复 + 数据完整性

### 6.2 内容合规
- **平台规则**：遵循小红书社区规范和算法偏好
- **法律法规**：符合广告法、消费者权益保护法等
- **行业标准**：遵循互联网内容管理相关标准
- **自律机制**：建立内容审核和用户举报机制

## 7. 运营策略

### 7.1 用户获取
- **内容营销**：小红书平台投放优质内容
- **KOL合作**：与头部博主合作推广
- **社群运营**：建立用户交流群和学习社区
- **口碑传播**：用户推荐奖励机制

### 7.2 用户留存
- **产品体验**：持续优化用户体验和功能
- **内容更新**：定期更新模板和热门话题
- **用户服务**：提供专业的客户服务支持
- **社区建设**：营造活跃的用户社区氛围

### 7.3 商业化
- **订阅模式**：多层次会员服务
- **增值服务**：定制化服务和咨询
- **API服务**：为企业提供接口服务
- **广告合作**：与品牌方合作推广

## 8. 项目规划

### 8.1 开发阶段
- **MVP阶段（3个月）**：核心文案生成功能、基础用户系统
- **V1.0阶段（6个月）**：完整功能、会员系统、移动端优化
- **V2.0阶段（9个月）**：AI优化、数据分析、运营工具
- **V3.0阶段（12个月）**：生态扩展、API开放、企业服务

### 8.2 团队配置
- **产品团队**：产品经理1人、UI设计师1人
- **技术团队**：前端工程师2人、后端工程师2人、AI工程师1人
- **运营团队**：运营经理1人、内容运营1人、客服1人
- **管理团队**：项目经理1人、测试工程师1人

### 8.3 预算规划
- **开发成本**：人员工资、技术服务、第三方接口
- **运营成本**：服务器、CDN、推广费用
- **其他成本**：办公场地、法务财务、知识产权

## 9. 风险评估

### 9.1 技术风险
- **AI模型效果**：生成质量不稳定、个性化不足
- **系统性能**：高并发处理、响应速度优化
- **数据安全**：用户隐私泄露、系统被攻击

### 9.2 市场风险
- **竞争加剧**：大厂入局、同类产品增多
- **政策变化**：平台规则调整、监管政策变化
- **用户需求**：需求变化、付费意愿下降

### 9.3 运营风险
- **内容合规**：违规内容产生、平台处罚
- **用户流失**：产品体验差、竞品吸引
- **商业化**：变现困难、成本过高

## 10. 成功指标

### 10.1 产品指标
- **用户规模**：注册用户数、月活用户数、日活用户数
- **使用频率**：人均使用次数、会话时长、功能使用率
- **用户留存**：次日留存率、7日留存率、30日留存率

### 10.2 商业指标
- **付费转化**：免费转付费率、会员续费率、ARPU值
- **收入增长**：月收入、年收入、收入增长率
- **成本控制**：获客成本、运营成本、利润率

### 10.3 质量指标
- **内容质量**：文案质量评分、用户满意度、投诉率
- **系统稳定**：系统可用率、响应时间、错误率
- **安全合规**：安全事件数、合规检查通过率

## 11. 详细技术实现方案

### 11.1 H5前端核心组件设计

#### 11.1.1 文案生成器组件
```vue
<!-- ContentGenerator.vue -->
<template>
  <div class="content-generator">
    <!-- 输入区域 -->
    <div class="input-section">
      <van-form @submit="generateContent">
        <!-- 文案类型选择 -->
        <van-field name="contentType" label="文案类型">
          <template #input>
            <van-picker
              v-model="form.contentType"
              :columns="contentTypes"
              @change="onContentTypeChange"
            />
          </template>
        </van-field>

        <!-- 行业分类 -->
        <van-field name="industry" label="行业分类">
          <template #input>
            <van-picker
              v-model="form.industry"
              :columns="industries"
            />
          </template>
        </van-field>

        <!-- 产品信息输入 -->
        <van-field
          v-model="form.productName"
          name="productName"
          label="产品名称"
          placeholder="请输入产品名称"
          required
        />

        <van-field
          v-model="form.productFeatures"
          name="productFeatures"
          label="产品特点"
          type="textarea"
          placeholder="请描述产品的主要特点和卖点"
          rows="3"
          required
        />

        <!-- 风格选择 -->
        <van-field name="style" label="文案风格">
          <template #input>
            <van-radio-group v-model="form.style" direction="horizontal">
              <van-radio name="grass" icon-size="16px">种草型</van-radio>
              <van-radio name="professional" icon-size="16px">专业型</van-radio>
              <van-radio name="friendly" icon-size="16px">亲民型</van-radio>
            </van-radio-group>
          </template>
        </van-field>

        <!-- 关键词标签 -->
        <van-field name="keywords" label="关键词">
          <template #input>
            <div class="keywords-input">
              <van-tag
                v-for="(keyword, index) in form.keywords"
                :key="index"
                closeable
                @close="removeKeyword(index)"
              >
                {{ keyword }}
              </van-tag>
              <van-field
                v-model="newKeyword"
                placeholder="添加关键词"
                @keyup.enter="addKeyword"
              />
            </div>
          </template>
        </van-field>

        <!-- 生成按钮 -->
        <div class="generate-btn-wrapper">
          <van-button
            type="primary"
            size="large"
            :loading="generating"
            @click="generateContent"
            block
          >
            {{ generating ? '生成中...' : '生成爆款文案' }}
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 结果展示区域 -->
    <div v-if="generatedContent" class="result-section">
      <div class="result-header">
        <h3>生成结果</h3>
        <div class="result-actions">
          <van-button size="small" @click="regenerate">重新生成</van-button>
          <van-button size="small" @click="saveContent">保存</van-button>
          <van-button size="small" @click="copyContent">复制</van-button>
        </div>
      </div>

      <!-- 标题区域 -->
      <div class="title-section">
        <h4>推荐标题</h4>
        <div class="title-options">
          <div
            v-for="(title, index) in generatedContent.titles"
            :key="index"
            class="title-option"
            :class="{ active: selectedTitle === index }"
            @click="selectTitle(index)"
          >
            <div class="title-text">{{ title.text }}</div>
            <div class="title-score">
              <van-rate v-model="title.score" readonly size="12px" />
              <span>{{ title.score }}/5</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 正文内容 -->
      <div class="content-section">
        <h4>正文内容</h4>
        <div class="content-text" v-html="formatContent(generatedContent.content)"></div>
      </div>

      <!-- 话题标签 -->
      <div class="hashtags-section">
        <h4>推荐话题</h4>
        <div class="hashtags">
          <van-tag
            v-for="(tag, index) in generatedContent.hashtags"
            :key="index"
            type="primary"
            size="medium"
          >
            #{{ tag }}
          </van-tag>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useContentStore } from '@/stores/content'
import { useUserStore } from '@/stores/user'
import { showToast, showConfirmDialog } from 'vant'

// 数据定义
const contentStore = useContentStore()
const userStore = useUserStore()

const generating = ref(false)
const generatedContent = ref(null)
const selectedTitle = ref(0)
const newKeyword = ref('')

const form = reactive({
  contentType: '种草文案',
  industry: '美妆护肤',
  productName: '',
  productFeatures: '',
  style: 'grass',
  keywords: []
})

const contentTypes = [
  { text: '种草文案', value: 'grass' },
  { text: '产品测评', value: 'review' },
  { text: '使用教程', value: 'tutorial' },
  { text: '好物推荐', value: 'recommendation' },
  { text: '穿搭分享', value: 'outfit' },
  { text: '美食分享', value: 'food' }
]

const industries = [
  { text: '美妆护肤', value: 'beauty' },
  { text: '服饰搭配', value: 'fashion' },
  { text: '美食饮品', value: 'food' },
  { text: '旅游出行', value: 'travel' },
  { text: '数码科技', value: 'tech' },
  { text: '家居生活', value: 'home' },
  { text: '母婴亲子', value: 'baby' },
  { text: '运动健身', value: 'fitness' }
]

// 方法定义
const addKeyword = () => {
  if (newKeyword.value.trim() && !form.keywords.includes(newKeyword.value.trim())) {
    form.keywords.push(newKeyword.value.trim())
    newKeyword.value = ''
  }
}

const removeKeyword = (index: number) => {
  form.keywords.splice(index, 1)
}

const generateContent = async () => {
  // 检查用户权限
  if (!userStore.canGenerate) {
    showConfirmDialog({
      title: '生成次数不足',
      message: '您今日的免费生成次数已用完，是否升级会员？',
      confirmButtonText: '升级会员',
      cancelButtonText: '取消'
    }).then(() => {
      // 跳转到会员页面
    })
    return
  }

  generating.value = true
  try {
    const result = await contentStore.generateContent({
      type: form.contentType,
      industry: form.industry,
      productName: form.productName,
      productFeatures: form.productFeatures,
      style: form.style,
      keywords: form.keywords
    })

    generatedContent.value = result
    selectedTitle.value = 0

    // 记录使用次数
    userStore.recordGeneration()

    showToast('文案生成成功！')
  } catch (error) {
    showToast('生成失败，请重试')
  } finally {
    generating.value = false
  }
}

const regenerate = () => {
  generateContent()
}

const saveContent = async () => {
  if (!generatedContent.value) return

  try {
    await contentStore.saveContent({
      title: generatedContent.value.titles[selectedTitle.value].text,
      content: generatedContent.value.content,
      hashtags: generatedContent.value.hashtags,
      type: form.contentType,
      industry: form.industry
    })

    showToast('保存成功！')
  } catch (error) {
    showToast('保存失败')
  }
}

const copyContent = () => {
  if (!generatedContent.value) return

  const title = generatedContent.value.titles[selectedTitle.value].text
  const content = generatedContent.value.content
  const hashtags = generatedContent.value.hashtags.map(tag => `#${tag}`).join(' ')

  const fullContent = `${title}\n\n${content}\n\n${hashtags}`

  navigator.clipboard.writeText(fullContent).then(() => {
    showToast('复制成功！')
  }).catch(() => {
    showToast('复制失败')
  })
}

const selectTitle = (index: number) => {
  selectedTitle.value = index
}

const formatContent = (content: string) => {
  // 格式化内容，添加换行、emoji等
  return content
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
}

const onContentTypeChange = (value: string) => {
  // 根据文案类型调整表单
  form.contentType = value
}
</script>

<style scoped>
.content-generator {
  padding: 16px;
}

.input-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.keywords-input {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.generate-btn-wrapper {
  margin-top: 24px;
}

.result-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.result-actions {
  display: flex;
  gap: 8px;
}

.title-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.title-option {
  padding: 12px;
  border: 1px solid #eee;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.title-option.active {
  border-color: #1989fa;
  background: #f0f8ff;
}

.title-text {
  font-weight: 500;
  margin-bottom: 8px;
}

.title-score {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.content-text {
  line-height: 1.6;
  color: #333;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
}

.hashtags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
```

#### 11.1.2 状态管理设计（Pinia）

```typescript
// stores/content.ts - 内容管理状态
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { contentApi } from '@/api/content'

export interface GeneratedContent {
  id: string
  titles: Array<{
    text: string
    score: number
    clickRate?: number
  }>
  content: string
  hashtags: string[]
  type: string
  industry: string
  style: string
  createdAt: string
  userId: string
}

export interface ContentTemplate {
  id: string
  name: string
  type: string
  industry: string
  template: string
  variables: string[]
  usage: number
  rating: number
}

export const useContentStore = defineStore('content', () => {
  // 状态
  const contents = ref<GeneratedContent[]>([])
  const templates = ref<ContentTemplate[]>([])
  const currentContent = ref<GeneratedContent | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const recentContents = computed(() =>
    contents.value
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 10)
  )

  const contentsByType = computed(() => {
    const grouped: Record<string, GeneratedContent[]> = {}
    contents.value.forEach(content => {
      if (!grouped[content.type]) {
        grouped[content.type] = []
      }
      grouped[content.type].push(content)
    })
    return grouped
  })

  const popularTemplates = computed(() =>
    templates.value
      .sort((a, b) => b.usage - a.usage)
      .slice(0, 20)
  )

  // 操作方法
  const generateContent = async (params: {
    type: string
    industry: string
    productName: string
    productFeatures: string
    style: string
    keywords: string[]
  }): Promise<GeneratedContent> => {
    loading.value = true
    error.value = null

    try {
      const result = await contentApi.generateContent(params)

      // 添加到本地状态
      contents.value.unshift(result)
      currentContent.value = result

      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : '生成失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const saveContent = async (content: Partial<GeneratedContent>): Promise<GeneratedContent> => {
    try {
      const saved = await contentApi.saveContent(content)

      // 更新本地状态
      const index = contents.value.findIndex(c => c.id === saved.id)
      if (index >= 0) {
        contents.value[index] = saved
      } else {
        contents.value.unshift(saved)
      }

      return saved
    } catch (err) {
      error.value = err instanceof Error ? err.message : '保存失败'
      throw err
    }
  }

  const deleteContent = async (id: string): Promise<void> => {
    try {
      await contentApi.deleteContent(id)

      // 从本地状态移除
      contents.value = contents.value.filter(c => c.id !== id)

      if (currentContent.value?.id === id) {
        currentContent.value = null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除失败'
      throw err
    }
  }

  const fetchContents = async (params?: {
    type?: string
    industry?: string
    page?: number
    limit?: number
  }): Promise<void> => {
    loading.value = true
    try {
      const result = await contentApi.getContents(params)
      contents.value = result.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取内容失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchTemplates = async (): Promise<void> => {
    try {
      const result = await contentApi.getTemplates()
      templates.value = result
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取模板失败'
      throw err
    }
  }

  const optimizeTitle = async (title: string, context: string): Promise<string[]> => {
    try {
      const result = await contentApi.optimizeTitle({ title, context })
      return result.suggestions
    } catch (err) {
      error.value = err instanceof Error ? err.message : '标题优化失败'
      throw err
    }
  }

  const getHashtagSuggestions = async (content: string, industry: string): Promise<string[]> => {
    try {
      const result = await contentApi.getHashtagSuggestions({ content, industry })
      return result.hashtags
    } catch (err) {
      error.value = err instanceof Error ? err.message : '话题推荐失败'
      throw err
    }
  }

  return {
    // 状态
    contents: readonly(contents),
    templates: readonly(templates),
    currentContent: readonly(currentContent),
    loading: readonly(loading),
    error: readonly(error),

    // 计算属性
    recentContents,
    contentsByType,
    popularTemplates,

    // 操作方法
    generateContent,
    saveContent,
    deleteContent,
    fetchContents,
    fetchTemplates,
    optimizeTitle,
    getHashtagSuggestions
  }
})
```

#### 11.1.3 API接口设计

```typescript
// api/content.ts - 内容相关API
import { http } from './http'
import type { GeneratedContent, ContentTemplate } from '@/stores/content'

export interface GenerateContentParams {
  type: string
  industry: string
  productName: string
  productFeatures: string
  style: string
  keywords: string[]
  userId?: string
}

export interface ContentListResponse {
  data: GeneratedContent[]
  total: number
  page: number
  limit: number
}

export const contentApi = {
  // 生成文案
  generateContent(params: GenerateContentParams): Promise<GeneratedContent> {
    return http.post('/api/content/generate', params)
  },

  // 保存文案
  saveContent(content: Partial<GeneratedContent>): Promise<GeneratedContent> {
    return http.post('/api/content/save', content)
  },

  // 获取文案列表
  getContents(params?: {
    type?: string
    industry?: string
    page?: number
    limit?: number
  }): Promise<ContentListResponse> {
    return http.get('/api/content/list', { params })
  },

  // 获取单个文案
  getContent(id: string): Promise<GeneratedContent> {
    return http.get(`/api/content/${id}`)
  },

  // 删除文案
  deleteContent(id: string): Promise<void> {
    return http.delete(`/api/content/${id}`)
  },

  // 获取模板列表
  getTemplates(params?: {
    type?: string
    industry?: string
  }): Promise<ContentTemplate[]> {
    return http.get('/api/templates', { params })
  },

  // 标题优化
  optimizeTitle(params: {
    title: string
    context: string
  }): Promise<{ suggestions: string[] }> {
    return http.post('/api/content/optimize-title', params)
  },

  // 话题推荐
  getHashtagSuggestions(params: {
    content: string
    industry: string
  }): Promise<{ hashtags: string[] }> {
    return http.post('/api/content/hashtag-suggestions', params)
  },

  // 内容分析
  analyzeContent(params: {
    content: string
    type: string
  }): Promise<{
    score: number
    suggestions: string[]
    keywords: string[]
  }> {
    return http.post('/api/content/analyze', params)
  },

  // 批量生成
  batchGenerate(params: {
    templates: string[]
    products: Array<{
      name: string
      features: string
      keywords: string[]
    }>
  }): Promise<GeneratedContent[]> {
    return http.post('/api/content/batch-generate', params)
  }
}
```

### 11.2 后端API实现

#### 11.2.1 核心文案生成服务

```typescript
// services/contentGenerator.ts - 文案生成核心服务
import OpenAI from 'openai'
import { ContentTemplate } from '../models/Template'
import { User } from '../models/User'

export class ContentGeneratorService {
  private openai: OpenAI
  private templates: Map<string, ContentTemplate> = new Map()

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    })
    this.loadTemplates()
  }

  async generateContent(params: {
    type: string
    industry: string
    productName: string
    productFeatures: string
    style: string
    keywords: string[]
    userId: string
  }) {
    try {
      // 1. 获取用户偏好和历史数据
      const user = await User.findById(params.userId)
      const userPreferences = await this.getUserPreferences(params.userId)

      // 2. 选择合适的模板
      const template = await this.selectTemplate(params.type, params.industry, params.style)

      // 3. 构建AI提示词
      const prompt = this.buildPrompt(template, params, userPreferences)

      // 4. 调用AI生成内容
      const aiResponse = await this.callAI(prompt)

      // 5. 后处理和优化
      const processedContent = await this.postProcess(aiResponse, params)

      // 6. 生成多个标题选项
      const titles = await this.generateTitles(processedContent.content, params)

      // 7. 推荐话题标签
      const hashtags = await this.generateHashtags(processedContent.content, params.industry)

      // 8. 计算质量评分
      const qualityScore = await this.calculateQualityScore(processedContent.content, titles)

      return {
        id: this.generateId(),
        titles,
        content: processedContent.content,
        hashtags,
        qualityScore,
        type: params.type,
        industry: params.industry,
        style: params.style,
        createdAt: new Date().toISOString(),
        userId: params.userId
      }
    } catch (error) {
      console.error('Content generation failed:', error)
      throw new Error('文案生成失败，请重试')
    }
  }

  private async selectTemplate(type: string, industry: string, style: string): Promise<ContentTemplate> {
    // 根据类型、行业、风格选择最合适的模板
    const templateKey = `${type}_${industry}_${style}`
    let template = this.templates.get(templateKey)

    if (!template) {
      // 如果没有精确匹配，使用通用模板
      template = this.templates.get(`${type}_general_${style}`) ||
                this.templates.get(`general_${industry}_${style}`) ||
                this.templates.get('general_general_general')
    }

    return template!
  }

  private buildPrompt(template: ContentTemplate, params: any, userPreferences: any): string {
    const basePrompt = `
你是一个专业的小红书文案创作专家，擅长创作爆款内容。

任务要求：
- 文案类型：${params.type}
- 行业领域：${params.industry}
- 文案风格：${params.style}
- 产品名称：${params.productName}
- 产品特点：${params.productFeatures}
- 关键词：${params.keywords.join(', ')}

用户偏好：
- 常用风格：${userPreferences.preferredStyle}
- 目标受众：${userPreferences.targetAudience}
- 内容长度：${userPreferences.contentLength}

小红书平台特点：
1. 标题要有吸引力，使用数字、符号、情感词汇
2. 内容要真实可信，体现用户体验
3. 适当使用emoji表情增加亲和力
4. 结构清晰，重点突出
5. 引导用户互动（点赞、收藏、评论）

模板结构：
${template.structure}

请生成一篇符合要求的小红书文案，确保：
- 标题吸引眼球且不夸大
- 内容真实可信有价值
- 语言风格符合目标用户群体
- 包含适当的互动引导
- 字数控制在200-500字之间
`

    return basePrompt
  }

  private async callAI(prompt: string): Promise<string> {
    const response = await this.openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: '你是一个专业的小红书文案创作专家，擅长创作符合平台特点的爆款内容。'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: 1000,
      temperature: 0.8,
      top_p: 0.9
    })

    return response.choices[0]?.message?.content || ''
  }

  private async postProcess(content: string, params: any): Promise<{ content: string }> {
    // 1. 敏感词过滤
    const filteredContent = await this.filterSensitiveWords(content)

    // 2. 格式优化
    const formattedContent = this.formatContent(filteredContent)

    // 3. 关键词密度检查
    const optimizedContent = this.optimizeKeywordDensity(formattedContent, params.keywords)

    return { content: optimizedContent }
  }

  private async generateTitles(content: string, params: any): Promise<Array<{ text: string; score: number }>> {
    const titlePrompt = `
基于以下内容，生成5个吸引人的小红书标题：

内容：${content}
产品：${params.productName}
行业：${params.industry}

要求：
1. 标题要有吸引力和点击欲望
2. 长度控制在15-25字
3. 可以使用数字、符号、emoji
4. 符合小红书用户习惯
5. 不要夸大宣传

请返回JSON格式：[{"text": "标题内容", "type": "标题类型"}]
`

    const response = await this.callAI(titlePrompt)
    const titles = JSON.parse(response)

    // 为每个标题计算吸引力评分
    return titles.map((title: any) => ({
      text: title.text,
      score: this.calculateTitleScore(title.text),
      type: title.type
    }))
  }

  private async generateHashtags(content: string, industry: string): Promise<string[]> {
    const hashtagPrompt = `
基于以下内容和行业，推荐10个相关的小红书话题标签：

内容：${content}
行业：${industry}

要求：
1. 标签要与内容高度相关
2. 包含热门话题和精准标签
3. 考虑标签的搜索热度
4. 避免过于宽泛的标签
5. 标签长度控制在2-8个字

请返回JSON数组格式：["标签1", "标签2", ...]
`

    const response = await this.callAI(hashtagPrompt)
    return JSON.parse(response)
  }

  private calculateTitleScore(title: string): number {
    let score = 3.0 // 基础分

    // 长度评分
    if (title.length >= 15 && title.length <= 25) score += 0.5

    // 数字评分
    if (/\d+/.test(title)) score += 0.3

    // 情感词评分
    const emotionWords = ['超级', '绝了', '必买', '神器', '爱了', '太好用']
    if (emotionWords.some(word => title.includes(word))) score += 0.4

    // 符号评分
    if (/[！？❗️❓💕✨🔥]/.test(title)) score += 0.2

    // 疑问句评分
    if (title.includes('？') || title.includes('吗')) score += 0.3

    return Math.min(5.0, score)
  }

  private async calculateQualityScore(content: string, titles: any[]): Promise<number> {
    // 综合评分算法
    let score = 0

    // 内容长度评分 (30%)
    const contentLength = content.length
    if (contentLength >= 200 && contentLength <= 500) {
      score += 30
    } else if (contentLength >= 100 && contentLength <= 800) {
      score += 20
    } else {
      score += 10
    }

    // 标题质量评分 (25%)
    const avgTitleScore = titles.reduce((sum, title) => sum + title.score, 0) / titles.length
    score += (avgTitleScore / 5) * 25

    // 结构完整性评分 (20%)
    const hasIntro = content.includes('开头') || content.length > 50
    const hasBody = content.length > 100
    const hasConclusion = content.includes('总结') || content.includes('推荐')
    const structureScore = (hasIntro ? 7 : 0) + (hasBody ? 7 : 0) + (hasConclusion ? 6 : 0)
    score += structureScore

    // 互动引导评分 (15%)
    const interactionWords = ['评论', '收藏', '点赞', '分享', '你们', '大家']
    const hasInteraction = interactionWords.some(word => content.includes(word))
    score += hasInteraction ? 15 : 5

    // 关键词密度评分 (10%)
    // 这里可以添加关键词密度检查逻辑
    score += 8

    return Math.min(100, Math.max(0, score))
  }

  private async filterSensitiveWords(content: string): Promise<string> {
    // 敏感词过滤逻辑
    const sensitiveWords = await this.getSensitiveWords()
    let filteredContent = content

    sensitiveWords.forEach(word => {
      const regex = new RegExp(word, 'gi')
      filteredContent = filteredContent.replace(regex, '*'.repeat(word.length))
    })

    return filteredContent
  }

  private formatContent(content: string): string {
    // 格式化内容
    return content
      .replace(/\n{3,}/g, '\n\n') // 移除多余换行
      .replace(/\s{2,}/g, ' ') // 移除多余空格
      .trim()
  }

  private optimizeKeywordDensity(content: string, keywords: string[]): string {
    // 关键词密度优化
    // 这里可以添加关键词密度检查和优化逻辑
    return content
  }

  private async getUserPreferences(userId: string): Promise<any> {
    // 获取用户偏好设置
    const user = await User.findById(userId)
    return user?.preferences || {
      preferredStyle: 'friendly',
      targetAudience: 'young_female',
      contentLength: 'medium'
    }
  }

  private async getSensitiveWords(): Promise<string[]> {
    // 从数据库或配置文件获取敏感词列表
    return [
      '违法', '欺诈', '虚假', '夸大', '绝对', '最好', '第一'
      // ... 更多敏感词
    ]
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  private async loadTemplates(): Promise<void> {
    // 从数据库加载模板
    const templates = await ContentTemplate.find()
    templates.forEach(template => {
      this.templates.set(template.key, template)
    })
  }
}
```

#### 11.2.2 数据库模型设计

```typescript
// models/User.ts - 用户模型
import mongoose, { Schema, Document } from 'mongoose'

export interface IUser extends Document {
  id: string
  email: string
  username: string
  password: string
  avatar?: string
  phone?: string
  membershipType: 'free' | 'premium' | 'professional' | 'enterprise'
  membershipExpiry?: Date
  dailyGenerationCount: number
  totalGenerationCount: number
  preferences: {
    preferredStyle: string
    targetAudience: string
    contentLength: string
    favoriteIndustries: string[]
    customKeywords: string[]
  }
  subscription?: {
    planId: string
    status: 'active' | 'cancelled' | 'expired'
    currentPeriodStart: Date
    currentPeriodEnd: Date
    autoRenew: boolean
  }
  createdAt: Date
  updatedAt: Date
  lastLoginAt?: Date
  isActive: boolean
}

const UserSchema = new Schema<IUser>({
  email: { type: String, required: true, unique: true },
  username: { type: String, required: true },
  password: { type: String, required: true },
  avatar: { type: String },
  phone: { type: String },
  membershipType: {
    type: String,
    enum: ['free', 'premium', 'professional', 'enterprise'],
    default: 'free'
  },
  membershipExpiry: { type: Date },
  dailyGenerationCount: { type: Number, default: 0 },
  totalGenerationCount: { type: Number, default: 0 },
  preferences: {
    preferredStyle: { type: String, default: 'friendly' },
    targetAudience: { type: String, default: 'young_female' },
    contentLength: { type: String, default: 'medium' },
    favoriteIndustries: [{ type: String }],
    customKeywords: [{ type: String }]
  },
  subscription: {
    planId: { type: String },
    status: {
      type: String,
      enum: ['active', 'cancelled', 'expired'],
      default: 'active'
    },
    currentPeriodStart: { type: Date },
    currentPeriodEnd: { type: Date },
    autoRenew: { type: Boolean, default: true }
  },
  lastLoginAt: { type: Date },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true
})

export const User = mongoose.model<IUser>('User', UserSchema)

// models/Content.ts - 内容模型
export interface IContent extends Document {
  id: string
  userId: string
  title: string
  content: string
  hashtags: string[]
  type: string
  industry: string
  style: string
  qualityScore: number
  generationParams: {
    productName: string
    productFeatures: string
    keywords: string[]
    template?: string
  }
  usage: {
    views: number
    copies: number
    shares: number
    likes: number
  }
  status: 'draft' | 'published' | 'archived'
  createdAt: Date
  updatedAt: Date
}

const ContentSchema = new Schema<IContent>({
  userId: { type: String, required: true, index: true },
  title: { type: String, required: true },
  content: { type: String, required: true },
  hashtags: [{ type: String }],
  type: { type: String, required: true, index: true },
  industry: { type: String, required: true, index: true },
  style: { type: String, required: true },
  qualityScore: { type: Number, min: 0, max: 100 },
  generationParams: {
    productName: { type: String },
    productFeatures: { type: String },
    keywords: [{ type: String }],
    template: { type: String }
  },
  usage: {
    views: { type: Number, default: 0 },
    copies: { type: Number, default: 0 },
    shares: { type: Number, default: 0 },
    likes: { type: Number, default: 0 }
  },
  status: {
    type: String,
    enum: ['draft', 'published', 'archived'],
    default: 'draft'
  }
}, {
  timestamps: true
})

export const Content = mongoose.model<IContent>('Content', ContentSchema)

// models/Template.ts - 模板模型
export interface ITemplate extends Document {
  id: string
  name: string
  description: string
  type: string
  industry: string
  style: string
  structure: string
  variables: string[]
  example: string
  usage: number
  rating: number
  isActive: boolean
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

const TemplateSchema = new Schema<ITemplate>({
  name: { type: String, required: true },
  description: { type: String, required: true },
  type: { type: String, required: true, index: true },
  industry: { type: String, required: true, index: true },
  style: { type: String, required: true },
  structure: { type: String, required: true },
  variables: [{ type: String }],
  example: { type: String },
  usage: { type: Number, default: 0 },
  rating: { type: Number, default: 0, min: 0, max: 5 },
  isActive: { type: Boolean, default: true },
  createdBy: { type: String, required: true }
}, {
  timestamps: true
})

export const Template = mongoose.model<ITemplate>('Template', TemplateSchema)
```

### 11.3 管理后台核心功能

#### 11.3.1 用户管理模块

```typescript
// admin/controllers/userController.ts
import { Request, Response } from 'express'
import { User } from '../models/User'
import { Content } from '../models/Content'

export class UserController {
  // 获取用户列表
  async getUsers(req: Request, res: Response) {
    try {
      const { page = 1, limit = 20, search, membershipType, status } = req.query

      const filter: any = {}

      if (search) {
        filter.$or = [
          { email: { $regex: search, $options: 'i' } },
          { username: { $regex: search, $options: 'i' } }
        ]
      }

      if (membershipType) {
        filter.membershipType = membershipType
      }

      if (status) {
        filter.isActive = status === 'active'
      }

      const users = await User.find(filter)
        .select('-password')
        .sort({ createdAt: -1 })
        .limit(Number(limit))
        .skip((Number(page) - 1) * Number(limit))

      const total = await User.countDocuments(filter)

      res.json({
        success: true,
        data: {
          users,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            pages: Math.ceil(total / Number(limit))
          }
        }
      })
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '获取用户列表失败',
        error: error.message
      })
    }
  }

  // 获取用户详情
  async getUserDetail(req: Request, res: Response) {
    try {
      const { userId } = req.params

      const user = await User.findById(userId).select('-password')
      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        })
      }

      // 获取用户生成的内容统计
      const contentStats = await Content.aggregate([
        { $match: { userId } },
        {
          $group: {
            _id: null,
            totalContents: { $sum: 1 },
            totalViews: { $sum: '$usage.views' },
            totalCopies: { $sum: '$usage.copies' },
            avgQualityScore: { $avg: '$qualityScore' }
          }
        }
      ])

      // 获取最近生成的内容
      const recentContents = await Content.find({ userId })
        .sort({ createdAt: -1 })
        .limit(10)
        .select('title type industry qualityScore createdAt')

      res.json({
        success: true,
        data: {
          user,
          stats: contentStats[0] || {
            totalContents: 0,
            totalViews: 0,
            totalCopies: 0,
            avgQualityScore: 0
          },
          recentContents
        }
      })
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '获取用户详情失败',
        error: error.message
      })
    }
  }

  // 更新用户状态
  async updateUserStatus(req: Request, res: Response) {
    try {
      const { userId } = req.params
      const { isActive, membershipType, membershipExpiry } = req.body

      const updateData: any = {}

      if (typeof isActive === 'boolean') {
        updateData.isActive = isActive
      }

      if (membershipType) {
        updateData.membershipType = membershipType
      }

      if (membershipExpiry) {
        updateData.membershipExpiry = new Date(membershipExpiry)
      }

      const user = await User.findByIdAndUpdate(
        userId,
        updateData,
        { new: true }
      ).select('-password')

      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        })
      }

      res.json({
        success: true,
        data: user,
        message: '用户状态更新成功'
      })
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '更新用户状态失败',
        error: error.message
      })
    }
  }

  // 重置用户每日生成次数
  async resetDailyCount(req: Request, res: Response) {
    try {
      const { userId } = req.params

      await User.findByIdAndUpdate(userId, {
        dailyGenerationCount: 0
      })

      res.json({
        success: true,
        message: '每日生成次数重置成功'
      })
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '重置失败',
        error: error.message
      })
    }
  }

  // 获取用户统计数据
  async getUserStats(req: Request, res: Response) {
    try {
      const { period = '7d' } = req.query

      let dateFilter: Date
      switch (period) {
        case '1d':
          dateFilter = new Date(Date.now() - 24 * 60 * 60 * 1000)
          break
        case '7d':
          dateFilter = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          break
        case '30d':
          dateFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          break
        default:
          dateFilter = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      }

      // 新用户注册统计
      const newUsers = await User.countDocuments({
        createdAt: { $gte: dateFilter }
      })

      // 活跃用户统计
      const activeUsers = await User.countDocuments({
        lastLoginAt: { $gte: dateFilter }
      })

      // 会员用户统计
      const membershipStats = await User.aggregate([
        {
          $group: {
            _id: '$membershipType',
            count: { $sum: 1 }
          }
        }
      ])

      // 用户增长趋势
      const growthTrend = await User.aggregate([
        {
          $match: {
            createdAt: { $gte: dateFilter }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$createdAt'
              }
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id': 1 } }
      ])

      res.json({
        success: true,
        data: {
          newUsers,
          activeUsers,
          membershipStats,
          growthTrend
        }
      })
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '获取统计数据失败',
        error: error.message
      })
    }
  }
}
```

## 12. 部署与运维方案

### 12.1 Docker容器化部署

```dockerfile
# Dockerfile.frontend - H5前端
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

```dockerfile
# Dockerfile.backend - 后端API
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["node", "dist/index.js"]
```

```yaml
# docker-compose.yml - 完整部署配置
version: '3.8'

services:
  # 前端H5应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      - VITE_API_BASE_URL=http://backend:3000
    depends_on:
      - backend
    networks:
      - app-network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongodb:27017/xiaohongshu_ai
      - REDIS_URL=redis://redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - mongodb
      - redis
    networks:
      - app-network

  # 管理后台
  admin:
    build:
      context: ./admin
      dockerfile: Dockerfile
    ports:
      - "8080:80"
    environment:
      - VITE_API_BASE_URL=http://backend:3000
    depends_on:
      - backend
    networks:
      - app-network

  # MongoDB数据库
  mongodb:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
    networks:
      - app-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - app-network

  # Nginx负载均衡
  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
      - admin
    networks:
      - app-network

volumes:
  mongodb_data:
  redis_data:

networks:
  app-network:
    driver: bridge
```

### 12.2 生产环境配置

```nginx
# nginx/nginx.conf - Nginx配置
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:3000;
    }

    upstream frontend {
        server frontend:80;
    }

    upstream admin {
        server admin:80;
    }

    # 主站点 - H5应用
    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        # 前端静态资源
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # API接口
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 文件上传大小限制
            client_max_body_size 10M;
        }

        # WebSocket支持
        location /ws/ {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # 管理后台
    server {
        listen 443 ssl http2;
        server_name admin.your-domain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        location / {
            proxy_pass http://admin;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # HTTP重定向到HTTPS
    server {
        listen 80;
        server_name your-domain.com admin.your-domain.com;
        return 301 https://$server_name$request_uri;
    }
}
```

### 12.3 监控与日志

```yaml
# monitoring/docker-compose.monitoring.yml
version: '3.8'

services:
  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123

  # ELK日志收集
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    ports:
      - "5044:5044"
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch

volumes:
  prometheus_data:
  grafana_data:
  elasticsearch_data:
```

## 13. 项目总结

### 13.1 核心技术亮点

1. **专业化AI模型**：针对小红书平台特点训练的专用文案生成模型
2. **H5架构优势**：跨平台兼容，无需下载安装，即用即走
3. **智能优化算法**：标题吸引力评分、内容质量评估、话题推荐
4. **实时数据分析**：用户行为分析、内容效果追踪、趋势预测
5. **完整的内容生态**：模板库、案例库、学习资源一体化

### 13.2 商业价值

1. **市场定位精准**：垂直化服务小红书生态，满足专业化需求
2. **用户价值明确**：显著提升内容创作效率和质量
3. **商业模式清晰**：分层订阅+增值服务，可持续盈利
4. **扩展性强**：可拓展到其他内容平台和营销场景

### 13.3 技术优势

1. **现代化技术栈**：Vue3 + TypeScript + Node.js，开发效率高
2. **微服务架构**：模块化设计，易于维护和扩展
3. **容器化部署**：Docker + Kubernetes，运维成本低
4. **完善的监控**：全链路监控，保障系统稳定性

### 13.4 风险控制

1. **内容安全**：多层次敏感词过滤和内容审核机制
2. **技术风险**：AI模型备份方案，多供应商策略
3. **合规风险**：严格遵循平台规则和法律法规
4. **竞争风险**：持续技术创新，建立用户粘性

### 13.5 发展规划

**短期目标（6个月）**：
- 完成MVP开发和测试
- 获得1万注册用户
- 实现基础商业化

**中期目标（12个月）**：
- 用户规模达到10万
- 月收入突破100万
- 拓展企业客户

**长期目标（24个月）**：
- 成为小红书文案工具第一品牌
- 拓展到其他内容平台
- 构建完整的内容营销生态

这个需求说明书为您的小红书爆款文案智能体服务提供了从产品定位到技术实现的完整解决方案。基于Vue3的H5架构确保了良好的用户体验和开发效率，而专业化的AI文案生成能力将成为产品的核心竞争优势。

您希望我进一步详细展开哪个技术模块，或者对某个具体功能进行深入设计吗？
