@echo off
echo 🚀 开始构建 VSCode 插件...

echo.
echo 📦 安装插件依赖...
call npm install
if %errorlevel% neq 0 (
    echo ❌ 插件依赖安装失败
    pause
    exit /b 1
)

echo.
echo 📦 安装前端依赖...
cd webview
call npm install
if %errorlevel% neq 0 (
    echo ❌ 前端依赖安装失败
    pause
    exit /b 1
)
cd ..

echo.
echo 🔨 编译插件核心...
call npm run compile:extension
if %errorlevel% neq 0 (
    echo ❌ 插件编译失败
    pause
    exit /b 1
)

echo.
echo 🔨 编译前端应用...
cd webview
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 前端编译失败
    pause
    exit /b 1
)
cd ..

echo.
echo 📦 打包 VSIX 文件...
call npx vsce package
if %errorlevel% neq 0 (
    echo ❌ VSIX 打包失败
    pause
    exit /b 1
)

echo.
echo 🎉 构建成功！
echo 📄 VSIX 文件已生成，可以在 VSCode 中安装使用
echo.
echo 📋 安装说明:
echo 1. 打开 VSCode
echo 2. 按 Ctrl+Shift+P 打开命令面板
echo 3. 输入 "Extensions: Install from VSIX..."
echo 4. 选择生成的 .vsix 文件
echo 5. 重启 VSCode 即可使用
echo.
pause
