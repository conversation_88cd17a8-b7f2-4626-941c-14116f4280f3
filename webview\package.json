{"name": "email-helper-webview", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.3.8", "element-plus": "^2.4.4", "pinia": "^2.1.7", "@element-plus/icons-vue": "^2.1.0"}, "devDependencies": {"@types/node": "^20.9.0", "@vitejs/plugin-vue": "^4.5.0", "typescript": "^5.2.2", "vite": "^5.0.0", "vue-tsc": "^1.8.22"}}