import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import { vscodeApi } from './utils/vscode-api'

// 创建Vue应用
const app = createApp(App)

// 状态管理
const pinia = createPinia()
app.use(pinia)

// UI组件库
app.use(ElementPlus)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局属性
app.config.globalProperties.$vscode = vscodeApi

// 挂载应用
app.mount('#app')
