@echo off
echo 🚀 创建 VSCode 插件 VSIX 包...

REM 创建临时目录
set TEMP_DIR=email-helper-temp
if exist %TEMP_DIR% rmdir /s /q %TEMP_DIR%
mkdir %TEMP_DIR%
cd %TEMP_DIR%

echo.
echo 📁 创建项目结构...
mkdir src
mkdir out

echo.
echo 📄 复制配置文件...
copy ..\email-helper-package.json package.json
copy ..\email-helper-tsconfig.json tsconfig.json

echo.
echo 📄 复制源代码...
copy ..\email-helper-extension.ts src\extension.ts

echo.
echo 📦 安装依赖...
call npm install

echo.
echo 🔨 编译 TypeScript...
call npx tsc

echo.
echo 📦 创建 VSIX 包...
call npx vsce package

echo.
echo 📋 复制 VSIX 文件到上级目录...
copy *.vsix ..

cd ..
echo.
echo 🧹 清理临时文件...
rmdir /s /q %TEMP_DIR%

echo.
echo 🎉 VSIX 包创建成功！
dir *.vsix

echo.
echo 📋 安装说明:
echo 1. 打开 VSCode
echo 2. 按 Ctrl+Shift+P 打开命令面板
echo 3. 输入 "Extensions: Install from VSIX..."
echo 4. 选择生成的 .vsix 文件
echo 5. 重启 VSCode 即可使用
echo.
echo 💡 使用方法:
echo 1. 按 Ctrl+Shift+P 打开命令面板
echo 2. 输入 "Email Helper: 打开邮箱助手"
echo 3. 在打开的面板中使用各项功能
echo.
pause
