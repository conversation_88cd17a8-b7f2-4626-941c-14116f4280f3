# VSCode邮箱验证码助手插件产品需求文档

## 1. 产品概述

### 1.1 产品定位
- **产品名称**：Email Verification Helper - VSCode邮箱验证码助手
- **目标用户**：需要频繁注册账号和处理邮箱验证的开发者
- **核心价值**：自动化邮箱生成、验证码获取和数据清理，提升开发测试效率

### 1.2 使用场景
- **场景1**：开发者需要测试用户注册流程，需要大量临时邮箱
- **场景2**：开发者需要快速获取邮箱验证码完成账号验证
- **场景3**：开发者需要清理VSCode中的敏感数据和历史记录

### 1.3 用户价值
- 提升开发测试效率，减少手动操作
- 自动化验证码获取，避免频繁切换邮箱客户端
- 保护开发隐私，快速清理敏感数据

## 2. 核心功能需求

### 功能1：邮箱生成器

**功能描述**：
一键生成指定后缀的临时邮箱地址，用于开发测试。

**详细需求**：
- **生成按钮**：界面提供"生成邮箱"按钮
- **邮箱后缀配置**：用户可配置邮箱后缀（如@temp-mail.org、@10minutemail.com等）
- **邮箱格式**：生成格式为随机字符串+配置后缀
- **结果显示**：生成的邮箱地址在插件界面中显示
- **复制功能**：支持一键复制生成的邮箱地址
- **历史记录**：保存最近生成的邮箱列表（可配置保存数量）

**交互流程**：
```mermaid
flowchart TD
    A[用户点击生成邮箱按钮] --> B[系统读取邮箱后缀配置]
    B --> C[生成随机字符串]
    C --> D[组合生成完整邮箱地址]
    D --> E[验证邮箱格式]
    E --> F{格式是否正确?}
    F -->|是| G[显示邮箱地址在界面]
    F -->|否| C
    G --> H[保存到历史记录]
    H --> I[用户可复制邮箱地址]
```

**配置选项**：
- 邮箱后缀设置
- 随机字符串长度设置
- 历史记录保存数量设置
- 邮箱格式模板设置

### 功能2：验证码获取器

**功能描述**：
自动读取指定邮箱的验证码邮件，解析并提取数字验证码。

**详细需求**：
- **获取按钮**：界面提供"获取验证码"按钮
- **邮箱配置**：用户配置要监控的邮箱地址
- **邮件筛选**：按邮件标题关键词筛选验证码邮件
- **验证码解析**：自动识别和提取邮件中的数字验证码
- **结果显示**：提取的验证码在插件界面中显示
- **复制功能**：支持一键复制验证码
- **自动刷新**：可设置自动检查新邮件的频率

**交互流程**：
```mermaid
flowchart TD
    A[用户点击获取验证码按钮] --> B[系统读取邮箱配置]
    B --> C[连接邮箱服务器]
    C --> D{连接是否成功?}
    D -->|否| E[显示连接错误信息]
    D -->|是| F[获取最新邮件列表]
    F --> G[按标题关键词筛选邮件]
    G --> H{找到匹配邮件?}
    H -->|否| I[显示未找到验证码邮件]
    H -->|是| J[读取邮件内容]
    J --> K[使用正则表达式提取验证码]
    K --> L{提取是否成功?}
    L -->|否| M[显示验证码提取失败]
    L -->|是| N[显示验证码在界面]
    N --> O[用户可复制验证码]
```

**配置选项**：
- 邮箱服务配置（IMAP/POP3设置）
- 邮件标题关键词设置
- 验证码格式规则设置
- 自动检查频率设置
- 邮件查找时间范围设置

### 功能3：数据清理器

**功能描述**：
参考augment-vip项目，清理VSCode中的敏感数据和历史记录。

**详细需求**：
- **清理按钮**：界面提供"清除数据"按钮
- **清理目标**：基于augment-vip项目的清理逻辑
  - 清理VSCode用户数据目录
  - 清理扩展数据和缓存
  - 清理工作空间历史记录
  - 清理搜索和文件历史
- **选择性清理**：用户可选择要清理的数据类型
- **清理预览**：显示将要清理的文件和数据
- **安全确认**：清理前需要用户确认操作
- **清理报告**：显示清理结果和统计信息

**交互流程**：
```mermaid
flowchart TD
    A[用户点击清除数据按钮] --> B[系统扫描VSCode数据目录]
    B --> C[识别可清理的数据类型]
    C --> D[显示清理选项界面]
    D --> E[用户选择要清理的数据类型]
    E --> F[系统扫描选中的数据]
    F --> G[显示清理预览]
    G --> H[用户确认清理操作]
    H --> I{用户是否确认?}
    I -->|否| J[取消清理操作]
    I -->|是| K[执行数据清理]
    K --> L[显示清理进度]
    L --> M[清理完成]
    M --> N[显示清理报告]
    N --> O[统计清理结果]
```

**清理范围**（参考augment-vip）：
- VSCode配置文件
- 扩展数据目录
- 工作空间设置
- 历史记录文件
- 缓存和临时文件

## 3. 用户界面设计

### 3.1 主界面布局

```
Email Verification Helper
┌─────────────────────────────────┐
│ 📧 邮箱生成器                    │
├─────────────────────────────────┤
│ 邮箱后缀: [@temp-mail.org ▼]    │
│ [生成邮箱] [复制]               │
│ 生成结果: <EMAIL> │
├─────────────────────────────────┤
│ 🔢 验证码获取器                  │
├─────────────────────────────────┤
│ 监控邮箱: [<EMAIL>]│
│ 标题关键词: [验证码,verification] │
│ [获取验证码] [复制]             │
│ 验证码: 123456                  │
├─────────────────────────────────┤
│ 🗑️ 数据清理器                   │
├─────────────────────────────────┤
│ ☑️ 用户配置  ☑️ 扩展数据        │
│ ☑️ 工作空间  ☑️ 历史记录        │
│ [清除数据] [预览]               │
│ 状态: 准备就绪                  │
└─────────────────────────────────┘
```

### 3.2 配置界面

```
设置
┌─────────────────────────────────┐
│ 邮箱生成设置                    │
├─────────────────────────────────┤
│ 默认后缀: [@temp-mail.org]      │
│ 用户名长度: [8] 字符            │
│ 历史记录: [10] 条               │
├─────────────────────────────────┤
│ 验证码获取设置                  │
├─────────────────────────────────┤
│ 邮箱服务: [IMAP ▼]              │
│ 服务器: [imap.temp-mail.org]    │
│ 端口: [993] SSL: [☑️]           │
│ 检查频率: [30] 秒               │
│ 关键词: [验证码,code,verification]│
├─────────────────────────────────┤
│ 数据清理设置                    │
├─────────────────────────────────┤
│ 清理确认: [☑️] 显示预览         │
│ 备份数据: [☑️] 清理前备份       │
│ 自动清理: [☐] 定时清理          │
└─────────────────────────────────┘
```

## 4. 技术实现要求

### 4.1 邮箱生成技术
- **随机字符串生成**：使用加密安全的随机数生成器
- **邮箱格式验证**：确保生成的邮箱格式正确
- **重复检测**：避免生成重复的邮箱地址
- **本地存储**：将生成历史保存在VSCode配置中

### 4.2 邮件读取技术
- **邮件协议支持**：支持IMAP、POP3协议
- **SSL/TLS加密**：确保邮件传输安全
- **邮件解析**：解析邮件头部和正文内容
- **验证码提取**：使用正则表达式提取数字验证码

### 4.3 数据清理技术
- **文件系统操作**：安全地删除文件和目录
- **权限检查**：确保有足够权限执行清理操作
- **备份机制**：清理前可选择备份重要数据
- **进度反馈**：显示清理进度和状态

## 5. 安全和隐私

### 5.1 数据安全
- **本地存储**：所有数据仅存储在本地
- **加密存储**：敏感配置信息加密保存
- **安全删除**：确保删除的数据无法恢复
- **权限最小化**：仅请求必要的系统权限

### 5.2 隐私保护
- **无数据上传**：不向外部服务器发送用户数据
- **邮箱隔离**：生成的临时邮箱与个人邮箱隔离
- **清理彻底**：确保清理操作不留痕迹
- **用户控制**：用户完全控制数据的生成和删除

## 6. 配置和设置

### 6.1 邮箱服务配置

```json
{
  "emailServices": [
    {
      "name": "TempMail",
      "domain": "@temp-mail.org",
      "imap": {
        "host": "imap.temp-mail.org",
        "port": 993,
        "secure": true
      }
    },
    {
      "name": "10MinuteMail",
      "domain": "@10minutemail.com",
      "imap": {
        "host": "imap.10minutemail.com",
        "port": 993,
        "secure": true
      }
    }
  ]
}
```

### 6.2 验证码提取规则

```json
{
  "extractionRules": [
    {
      "name": "数字验证码",
      "pattern": "\\b\\d{4,8}\\b",
      "description": "4-8位数字验证码"
    },
    {
      "name": "字母数字混合",
      "pattern": "\\b[A-Z0-9]{6}\\b",
      "description": "6位字母数字验证码"
    }
  ]
}
```

### 6.3 清理目标配置

```json
{
  "cleanupTargets": [
    {
      "name": "用户配置",
      "path": "User/settings.json",
      "description": "VSCode用户设置文件"
    },
    {
      "name": "工作空间历史",
      "path": "User/workspaceStorage",
      "description": "工作空间存储目录"
    },
    {
      "name": "扩展数据",
      "path": "extensions",
      "description": "扩展数据目录"
    },
    {
      "name": "搜索历史",
      "path": "User/History",
      "description": "搜索和文件历史"
    },
    {
      "name": "缓存文件",
      "path": "CachedExtensions",
      "description": "扩展缓存目录"
    }
  ]
}
```

## 7. 整体交互流程

### 7.1 插件启动流程

```mermaid
flowchart TD
    A[VSCode启动] --> B[加载插件]
    B --> C[读取配置文件]
    C --> D[初始化界面]
    D --> E[显示主界面]
    E --> F[等待用户操作]
```

### 7.2 完整工作流程

```mermaid
flowchart TD
    A[用户启动插件] --> B{选择功能}
    B -->|生成邮箱| C[邮箱生成流程]
    B -->|获取验证码| D[验证码获取流程]
    B -->|清理数据| E[数据清理流程]

    C --> F[生成随机邮箱]
    F --> G[显示邮箱地址]
    G --> H[用户复制使用]

    D --> I[连接邮箱服务]
    I --> J[筛选验证码邮件]
    J --> K[提取验证码]
    K --> L[显示验证码]

    E --> M[选择清理目标]
    M --> N[预览清理内容]
    N --> O[执行清理操作]
    O --> P[显示清理结果]

    H --> Q[继续使用或退出]
    L --> Q
    P --> Q
```

## 8. 开发优先级

### 8.1 MVP版本（第一阶段）
- **邮箱生成器**：基础邮箱生成功能
- **验证码获取器**：简单的验证码获取功能
- **数据清理器**：基础数据清理功能
- **用户界面**：简单的操作界面

### 8.2 完整版本（第二阶段）
- **多邮箱服务支持**：支持多种临时邮箱服务
- **高级验证码提取**：支持多种验证码格式
- **完整清理选项**：参考augment-vip的完整清理功能
- **配置界面**：完整的设置和配置界面

### 8.3 增强版本（第三阶段）
- **自动化功能**：自动检查邮件和验证码
- **批量操作**：批量生成邮箱和处理验证码
- **高级安全**：增强的数据保护和隐私功能
- **用户体验优化**：界面美化和交互优化

## 9. 技术栈

### 9.1 前端技术
- **框架**：Vue 3 + Composition API
- **构建工具**：Vite
- **UI组件**：Element Plus
- **状态管理**：Pinia
- **类型支持**：TypeScript

### 9.2 插件核心
- **开发语言**：TypeScript
- **插件API**：VSCode Extension API
- **邮件处理**：node-imap / node-pop3
- **文件操作**：Node.js fs模块
- **加密存储**：crypto模块

### 9.3 构建和打包
- **插件构建**：webpack
- **前端构建**：Vite
- **代码检查**：ESLint + Prettier
- **测试框架**：Jest

## 10. 成功指标

### 10.1 功能指标
- **邮箱生成成功率**：>99%
- **验证码提取准确率**：>95%
- **数据清理完成率**：>99%
- **插件启动时间**：<3秒

### 10.2 用户指标
- **插件安装量**：第一年达到1,000+
- **用户活跃度**：月活跃用户率>60%
- **用户满意度**：VSCode市场评分>4.0
- **使用频率**：用户平均每周使用>3次

### 10.3 质量指标
- **错误率**：<1%
- **崩溃率**：<0.1%
- **响应时间**：核心功能响应<2秒
- **兼容性**：支持VSCode 1.60.0+版本

## 11. 风险评估

### 11.1 技术风险
- **邮箱服务限制**：临时邮箱服务可能有访问限制
- **VSCode API变更**：VSCode更新可能影响插件功能
- **跨平台兼容性**：不同操作系统的文件路径差异

### 11.2 安全风险
- **数据泄露**：邮箱凭据和验证码的安全存储
- **权限滥用**：文件删除操作的安全控制
- **恶意使用**：防止插件被用于恶意目的

### 11.3 合规风险
- **隐私法规**：符合GDPR等隐私保护法规
- **服务条款**：遵守邮箱服务商的使用条款
- **平台政策**：符合VSCode市场的发布政策

## 12. 后续规划

### 12.1 功能扩展
- 支持更多邮箱服务提供商
- 增加短信验证码获取功能
- 集成更多开发工具和平台
- 提供API供其他插件使用

### 12.2 用户体验优化
- 界面主题定制
- 快捷键支持
- 多语言支持
- 无障碍功能支持

### 12.3 生态建设
- 开源社区建设
- 用户反馈收集
- 文档和教程完善
- 与其他开发工具集成

---

**文档版本**：v1.0
**创建日期**：2024年12月
**最后更新**：2024年12月
