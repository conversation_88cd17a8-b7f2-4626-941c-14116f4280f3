<template>
  <el-card class="email-generator-card">
    <template #header>
      <div class="card-header">
        <h3>
          <el-icon><Message /></el-icon>
          邮箱生成器
        </h3>
      </div>
    </template>

    <div class="generator-content">
      <!-- 配置区域 -->
      <div class="config-section">
        <el-form :model="form" label-width="100px" size="default">
          <el-form-item label="邮箱后缀">
            <el-select v-model="form.emailSuffix" placeholder="选择邮箱后缀" style="width: 100%">
              <el-option
                v-for="suffix in emailSuffixes"
                :key="suffix"
                :label="suffix"
                :value="suffix"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="用户名长度">
            <el-input-number
              v-model="form.usernameLength"
              :min="4"
              :max="20"
              :step="1"
              style="width: 100%"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 生成按钮 -->
      <div class="action-section">
        <el-button
          type="primary"
          size="large"
          :loading="generating"
          @click="generateEmail"
          :icon="Plus"
          style="width: 100%"
        >
          {{ generating ? '生成中...' : '生成邮箱' }}
        </el-button>
      </div>

      <!-- 结果显示 -->
      <div v-if="generatedEmail" class="result-section">
        <el-alert
          title="邮箱生成成功"
          type="success"
          :closable="false"
          show-icon
        />
        
        <div class="email-result">
          <el-input
            v-model="generatedEmail"
            readonly
            size="large"
          >
            <template #append>
              <el-button @click="copyEmail" :icon="CopyDocument">
                复制
              </el-button>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 历史记录 -->
      <div v-if="emailHistory.length > 0" class="history-section">
        <div class="history-header">
          <h4>历史记录</h4>
          <el-button size="small" text @click="clearHistory">
            清空历史
          </el-button>
        </div>
        
        <div class="history-list">
          <div
            v-for="(item, index) in emailHistory"
            :key="index"
            class="history-item"
          >
            <span class="email-text">{{ item.email }}</span>
            <div class="item-actions">
              <el-button
                size="small"
                text
                @click="copyEmail(item.email)"
                :icon="CopyDocument"
              >
                复制
              </el-button>
              <el-button
                size="small"
                text
                @click="useEmail(item.email)"
                :icon="Select"
              >
                使用
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Message, Plus, CopyDocument, Select } from '@element-plus/icons-vue'
import { vscodeApi } from '@/utils/vscode-api'
import { useAppStore } from '@/stores/app'

// 状态管理
const appStore = useAppStore()

// 响应式数据
const generating = ref(false)
const generatedEmail = ref('')
const emailHistory = ref<Array<{ email: string; createdAt: Date }>>([])

// 表单数据
const form = reactive({
  emailSuffix: '@temp-mail.org',
  usernameLength: 8
})

// 邮箱后缀选项
const emailSuffixes = ref([
  '@temp-mail.org',
  '@10minutemail.com',
  '@guerrillamail.com',
  '@mailinator.com'
])

// 生命周期
onMounted(() => {
  loadConfig()
  loadHistory()
})

// 方法
const loadConfig = () => {
  if (appStore.config) {
    form.emailSuffix = appStore.config.defaultEmailSuffix
    form.usernameLength = appStore.config.usernameLength
  }
}

const loadHistory = () => {
  // 从本地存储加载历史记录
  const saved = localStorage.getItem('emailHistory')
  if (saved) {
    try {
      emailHistory.value = JSON.parse(saved)
    } catch (error) {
      console.error('加载历史记录失败:', error)
    }
  }
}

const saveHistory = () => {
  // 保存到本地存储
  localStorage.setItem('emailHistory', JSON.stringify(emailHistory.value))
}

const generateEmail = async () => {
  generating.value = true
  try {
    const email = await vscodeApi.generateEmail()
    generatedEmail.value = email
    
    // 添加到历史记录
    addToHistory(email)
    
    ElMessage.success('邮箱生成成功')
  } catch (error) {
    ElMessage.error('邮箱生成失败: ' + (error instanceof Error ? error.message : '未知错误'))
  } finally {
    generating.value = false
  }
}

const copyEmail = async (email?: string) => {
  const emailToCopy = email || generatedEmail.value
  if (!emailToCopy) return

  try {
    await vscodeApi.copyToClipboard(emailToCopy)
    ElMessage.success('邮箱已复制到剪贴板')
  } catch (error) {
    // 降级到浏览器API
    try {
      await navigator.clipboard.writeText(emailToCopy)
      ElMessage.success('邮箱已复制到剪贴板')
    } catch (fallbackError) {
      ElMessage.error('复制失败')
    }
  }
}

const useEmail = (email: string) => {
  generatedEmail.value = email
  ElMessage.info('已选择邮箱: ' + email)
}

const addToHistory = (email: string) => {
  // 检查是否已存在
  const existingIndex = emailHistory.value.findIndex(item => item.email === email)
  if (existingIndex !== -1) {
    emailHistory.value.splice(existingIndex, 1)
  }

  // 添加到开头
  emailHistory.value.unshift({
    email,
    createdAt: new Date()
  })

  // 限制历史记录数量
  const limit = appStore.config?.historyLimit || 10
  if (emailHistory.value.length > limit) {
    emailHistory.value = emailHistory.value.slice(0, limit)
  }

  saveHistory()
}

const clearHistory = () => {
  emailHistory.value = []
  saveHistory()
  ElMessage.success('历史记录已清空')
}
</script>

<style scoped>
.email-generator-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-primary);
}

.generator-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.config-section {
  background: var(--el-bg-color-page);
  padding: 16px;
  border-radius: 6px;
}

.action-section {
  display: flex;
  justify-content: center;
}

.result-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.email-result {
  margin-top: 12px;
}

.history-section {
  border-top: 1px solid var(--el-border-color);
  padding-top: 16px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.history-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--el-bg-color-page);
  border-radius: 4px;
  border: 1px solid var(--el-border-color);
}

.email-text {
  font-family: monospace;
  color: var(--el-text-color-primary);
  flex: 1;
}

.item-actions {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .history-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .item-actions {
    align-self: flex-end;
  }
}
</style>
