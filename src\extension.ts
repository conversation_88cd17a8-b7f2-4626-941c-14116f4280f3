import * as vscode from 'vscode';
import { EmailHelperPanel } from './panels/EmailHelperPanel';
import { EmailService } from './services/EmailService';
import { DataCleanupService } from './services/DataCleanupService';
import { ConfigService } from './services/ConfigService';

export function activate(context: vscode.ExtensionContext) {
    console.log('Email Verification Helper 插件已激活');

    // 初始化服务
    const configService = new ConfigService();
    const emailService = new EmailService(configService);
    const dataCleanupService = new DataCleanupService();

    // 注册命令：打开邮箱助手面板
    const openPanelCommand = vscode.commands.registerCommand('emailHelper.openPanel', () => {
        EmailHelperPanel.createOrShow(context.extensionUri, {
            emailService,
            dataCleanupService,
            configService
        });
    });

    // 注册命令：生成邮箱
    const generateEmailCommand = vscode.commands.registerCommand('emailHelper.generateEmail', async () => {
        try {
            const email = await emailService.generateEmail();
            vscode.window.showInformationMessage(`生成邮箱: ${email}`);
            // 复制到剪贴板
            await vscode.env.clipboard.writeText(email);
            vscode.window.showInformationMessage('邮箱已复制到剪贴板');
        } catch (error) {
            vscode.window.showErrorMessage(`生成邮箱失败: ${error}`);
        }
    });

    // 注册命令：获取验证码
    const getVerificationCodeCommand = vscode.commands.registerCommand('emailHelper.getVerificationCode', async () => {
        try {
            const code = await emailService.getLatestVerificationCode();
            if (code) {
                vscode.window.showInformationMessage(`验证码: ${code}`);
                // 复制到剪贴板
                await vscode.env.clipboard.writeText(code);
                vscode.window.showInformationMessage('验证码已复制到剪贴板');
            } else {
                vscode.window.showWarningMessage('未找到验证码邮件');
            }
        } catch (error) {
            vscode.window.showErrorMessage(`获取验证码失败: ${error}`);
        }
    });

    // 注册命令：清理数据
    const cleanupDataCommand = vscode.commands.registerCommand('emailHelper.cleanupData', async () => {
        try {
            const result = await vscode.window.showWarningMessage(
                '确定要清理VSCode数据吗？此操作不可撤销。',
                { modal: true },
                '确定清理',
                '取消'
            );

            if (result === '确定清理') {
                const cleanupResult = await dataCleanupService.cleanupData();
                vscode.window.showInformationMessage(
                    `数据清理完成！清理了 ${cleanupResult.filesDeleted} 个文件，释放空间 ${cleanupResult.spaceFreed} KB`
                );
            }
        } catch (error) {
            vscode.window.showErrorMessage(`数据清理失败: ${error}`);
        }
    });

    // 添加到订阅列表
    context.subscriptions.push(
        openPanelCommand,
        generateEmailCommand,
        getVerificationCodeCommand,
        cleanupDataCommand
    );

    // 监听配置变化
    const configChangeListener = vscode.workspace.onDidChangeConfiguration(event => {
        if (event.affectsConfiguration('emailHelper')) {
            configService.reloadConfig();
        }
    });

    context.subscriptions.push(configChangeListener);
}

export function deactivate() {
    console.log('Email Verification Helper 插件已停用');
}
