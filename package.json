{"name": "email-verification-helper", "displayName": "Email Verification Helper", "description": "VSCode邮箱验证码助手 - 自动生成邮箱、获取验证码、清理数据", "version": "1.0.0", "publisher": "email-helper-dev", "repository": {"type": "git", "url": "https://github.com/your-username/email-verification-helper.git"}, "bugs": {"url": "https://github.com/your-username/email-verification-helper/issues"}, "homepage": "https://github.com/your-username/email-verification-helper#readme", "license": "MIT", "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "keywords": ["email", "verification", "code", "helper", "邮箱", "验证码"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "emailHelper.openPanel", "title": "打开邮箱助手", "category": "Em<PERSON>er"}], "menus": {"commandPalette": [{"command": "emailHelper.openPanel", "title": "Email Helper: 打开邮箱助手"}]}, "configuration": {"title": "Em<PERSON>er", "properties": {"emailHelper.defaultEmailSuffix": {"type": "string", "default": "@temp-mail.org", "description": "默认邮箱后缀"}, "emailHelper.usernameLength": {"type": "number", "default": 8, "description": "生成邮箱用户名长度"}, "emailHelper.historyLimit": {"type": "number", "default": 10, "description": "历史记录保存数量"}, "emailHelper.checkInterval": {"type": "number", "default": 30, "description": "邮件检查间隔（秒）"}, "emailHelper.extractionKeywords": {"type": "array", "default": ["验证码", "verification", "code"], "description": "验证码邮件关键词"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "npm run compile:extension && npm run compile:webview", "compile:extension": "tsc -p ./", "compile:webview": "cd webview && npm run build", "watch": "npm run watch:extension & npm run watch:webview", "watch:extension": "tsc -watch -p ./", "watch:webview": "cd webview && npm run dev", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "node scripts/package.js", "package:simple": "vsce package"}, "devDependencies": {"@types/vscode": "^1.60.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "@vscode/test-electron": "^2.2.0", "vsce": "^2.15.0"}, "dependencies": {"node-imap": "^0.9.6", "mailparser": "^3.6.5", "crypto": "^1.0.1"}}