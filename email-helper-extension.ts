import * as vscode from 'vscode';

export function activate(context: vscode.ExtensionContext) {
    console.log('Email Verification Helper 插件已激活');

    const openPanelCommand = vscode.commands.registerCommand('emailHelper.openPanel', () => {
        const panel = vscode.window.createWebviewPanel(
            'emailHelper',
            'Email Verification Helper',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                localResourceRoots: [context.extensionUri]
            }
        );

        panel.webview.html = getWebviewContent();

        panel.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.command) {
                    case 'generateEmail':
                        const email = generateRandomEmail();
                        panel.webview.postMessage({
                            command: 'emailGenerated',
                            data: { email }
                        });
                        break;

                    case 'getVerificationCode':
                        const code = generateMockVerificationCode();
                        panel.webview.postMessage({
                            command: 'verificationCodeReceived',
                            data: { code }
                        });
                        break;

                    case 'copyToClipboard':
                        await vscode.env.clipboard.writeText(message.data?.text || '');
                        vscode.window.showInformationMessage('已复制到剪贴板');
                        break;
                }
            },
            undefined,
            context.subscriptions
        );
    });

    context.subscriptions.push(openPanelCommand);
}

function generateRandomEmail(): string {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let username = '';
    for (let i = 0; i < 8; i++) {
        username += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return `${username}@temp-mail.org`;
}

function generateMockVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
}

function getWebviewContent(): string {
    return `<!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification Helper</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                padding: 20px;
                background: var(--vscode-editor-background);
                color: var(--vscode-editor-foreground);
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
            }
            .section {
                margin-bottom: 30px;
                padding: 20px;
                border: 1px solid var(--vscode-panel-border);
                border-radius: 6px;
                background: var(--vscode-panel-background);
            }
            .section h3 {
                margin-top: 0;
                color: var(--vscode-textLink-foreground);
            }
            button {
                background: var(--vscode-button-background);
                color: var(--vscode-button-foreground);
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                cursor: pointer;
                margin: 5px;
            }
            button:hover {
                background: var(--vscode-button-hoverBackground);
            }
            input {
                background: var(--vscode-input-background);
                color: var(--vscode-input-foreground);
                border: 1px solid var(--vscode-input-border);
                padding: 8px 12px;
                border-radius: 4px;
                width: 300px;
                margin: 5px;
            }
            .result {
                margin-top: 10px;
                padding: 10px;
                background: var(--vscode-textCodeBlock-background);
                border-radius: 4px;
                font-family: monospace;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>📧 Email Verification Helper</h1>
            
            <div class="section">
                <h3>📧 邮箱生成器</h3>
                <button onclick="generateEmail()">生成邮箱</button>
                <button onclick="copyEmail()" id="copyEmailBtn" style="display:none">复制邮箱</button>
                <div id="emailResult" class="result" style="display:none"></div>
            </div>

            <div class="section">
                <h3>🔢 验证码获取器</h3>
                <input type="text" id="emailInput" placeholder="输入邮箱地址" />
                <button onclick="getVerificationCode()">获取验证码</button>
                <button onclick="copyCode()" id="copyCodeBtn" style="display:none">复制验证码</button>
                <div id="codeResult" class="result" style="display:none"></div>
            </div>
        </div>

        <script>
            const vscode = acquireVsCodeApi();
            let currentEmail = '';
            let currentCode = '';

            function generateEmail() {
                vscode.postMessage({ command: 'generateEmail' });
            }

            function getVerificationCode() {
                const email = document.getElementById('emailInput').value;
                vscode.postMessage({ command: 'getVerificationCode', data: { email } });
            }

            function copyEmail() {
                if (currentEmail) {
                    vscode.postMessage({ command: 'copyToClipboard', data: { text: currentEmail } });
                }
            }

            function copyCode() {
                if (currentCode) {
                    vscode.postMessage({ command: 'copyToClipboard', data: { text: currentCode } });
                }
            }

            window.addEventListener('message', event => {
                const message = event.data;
                
                switch (message.command) {
                    case 'emailGenerated':
                        currentEmail = message.data.email;
                        document.getElementById('emailResult').innerHTML = '生成的邮箱: ' + currentEmail;
                        document.getElementById('emailResult').style.display = 'block';
                        document.getElementById('copyEmailBtn').style.display = 'inline-block';
                        break;

                    case 'verificationCodeReceived':
                        currentCode = message.data.code;
                        document.getElementById('codeResult').innerHTML = '验证码: ' + currentCode;
                        document.getElementById('codeResult').style.display = 'block';
                        document.getElementById('copyCodeBtn').style.display = 'inline-block';
                        break;
                }
            });
        </script>
    </body>
    </html>`;
}

export function deactivate() {}
