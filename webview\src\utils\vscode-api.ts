interface VSCodeAPI {
  postMessage(message: any): void
  getState(): any
  setState(state: any): void
}

declare global {
  interface Window {
    acquireVsCodeApi(): VSCodeAPI
  }
}

class VSCodeApiWrapper {
  private vscode: VSCodeAPI | null = null
  private messageId = 0
  private pendingMessages = new Map<number, {
    resolve: (value: any) => void
    reject: (error: any) => void
  }>()

  constructor() {
    try {
      this.vscode = window.acquireVsCodeApi()
      this.setupMessageListener()
    } catch (error) {
      console.warn('VSCode API不可用，可能在开发环境中运行')
      // 在开发环境中提供模拟API
      this.setupMockAPI()
    }
  }

  private setupMessageListener() {
    window.addEventListener('message', (event) => {
      const message = event.data
      
      if (message.id && this.pendingMessages.has(message.id)) {
        const { resolve, reject } = this.pendingMessages.get(message.id)!
        this.pendingMessages.delete(message.id)
        
        if (message.error) {
          reject(new Error(message.error))
        } else {
          resolve(message)
        }
      }
    })
  }

  private setupMockAPI() {
    // 开发环境模拟API
    this.vscode = {
      postMessage: (message: any) => {
        console.log('Mock VSCode API - 发送消息:', message)
        // 模拟异步响应
        setTimeout(() => {
          this.handleMockResponse(message)
        }, 500)
      },
      getState: () => ({}),
      setState: (state: any) => {
        console.log('Mock VSCode API - 设置状态:', state)
      }
    }
  }

  private handleMockResponse(message: any) {
    let mockResponse: any = { id: message.id }

    switch (message.command) {
      case 'generateEmail':
        mockResponse.data = {
          email: `test${Math.floor(Math.random() * 10000)}@temp-mail.org`
        }
        mockResponse.command = 'emailGenerated'
        break

      case 'getVerificationCode':
        mockResponse.data = {
          code: Math.floor(100000 + Math.random() * 900000).toString()
        }
        mockResponse.command = 'verificationCodeReceived'
        break

      case 'cleanupData':
        mockResponse.data = {
          success: true,
          filesDeleted: Math.floor(Math.random() * 50),
          spaceFreed: Math.floor(Math.random() * 1000),
          errors: [],
          details: ['清理搜索历史', '清理工作空间历史', '清理缓存文件']
        }
        mockResponse.command = 'cleanupCompleted'
        break

      case 'getConfig':
        mockResponse.data = {
          defaultEmailSuffix: '@temp-mail.org',
          usernameLength: 8,
          historyLimit: 10,
          checkInterval: 30,
          extractionKeywords: ['验证码', 'verification', 'code']
        }
        mockResponse.command = 'configReceived'
        break

      case 'updateConfig':
        mockResponse.data = { success: true }
        mockResponse.command = 'configUpdated'
        break

      default:
        mockResponse.error = '未知命令'
    }

    // 模拟消息事件
    window.dispatchEvent(new MessageEvent('message', {
      data: mockResponse
    }))
  }

  async postMessage(message: any): Promise<any> {
    if (!this.vscode) {
      throw new Error('VSCode API不可用')
    }

    return new Promise((resolve, reject) => {
      const id = ++this.messageId
      this.pendingMessages.set(id, { resolve, reject })
      
      this.vscode!.postMessage({
        ...message,
        id
      })
      
      // 设置超时
      setTimeout(() => {
        if (this.pendingMessages.has(id)) {
          this.pendingMessages.delete(id)
          reject(new Error('请求超时'))
        }
      }, 30000)
    })
  }

  getState() {
    return this.vscode?.getState() || {}
  }

  setState(state: any) {
    this.vscode?.setState(state)
  }

  // 便捷方法
  async generateEmail(): Promise<string> {
    const response = await this.postMessage({ command: 'generateEmail' })
    return response.data.email
  }

  async getVerificationCode(email?: string): Promise<string> {
    const response = await this.postMessage({ 
      command: 'getVerificationCode',
      data: { email }
    })
    return response.data.code
  }

  async cleanupData(options?: any): Promise<any> {
    const response = await this.postMessage({ 
      command: 'cleanupData',
      data: { options }
    })
    return response.data
  }

  async getConfig(): Promise<any> {
    const response = await this.postMessage({ command: 'getConfig' })
    return response.data
  }

  async updateConfig(config: any): Promise<void> {
    await this.postMessage({ 
      command: 'updateConfig',
      data: config
    })
  }

  async copyToClipboard(text: string): Promise<void> {
    await this.postMessage({ 
      command: 'copyToClipboard',
      data: { text }
    })
  }
}

export const vscodeApi = new VSCodeApiWrapper()
