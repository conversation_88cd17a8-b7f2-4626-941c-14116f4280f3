# PromptX MCP服务产品需求清单

## 1. 产品概述

### 1.1 产品定位
- **产品名称**：PromptX Cloud
- **产品定位**：基于PromptX框架的云端AI角色管理平台
- **核心价值**：让用户通过可视化界面创建、管理和分享AI专业角色，通过MCP协议无缝集成到各种AI助手中

### 1.2 目标用户
- **主要用户**：AI应用开发者、企业AI团队、提示词工程师
- **次要用户**：AI爱好者、教育工作者、内容创作者
- **企业用户**：需要定制AI角色的中大型企业

### 1.3 商业模式
- **免费版**：基础角色创建（限5个）、公共模板使用
- **专业版**：无限角色、团队协作、API调用（¥99/月）
- **企业版**：私有部署、定制开发、技术支持（¥999/月起）

## 2. 核心功能模块

### 2.1 角色管理系统

#### 2.1.1 角色CRUD功能
- **角色创建**
  - 从空白模板创建
  - 从预设模板创建
  - 导入现有PromptX角色文件
  - 克隆现有角色
- **角色编辑**
  - 可视化编辑器（支持PromptX格式）
  - 实时语法检查和验证
  - 预览功能
  - 版本对比
- **角色管理**
  - 角色列表和搜索
  - 分类和标签管理
  - 批量操作
  - 回收站功能

#### 2.1.2 角色模板库
- **官方模板**
  - 预置专业角色模板（产品经理、开发者、设计师等）
  - 行业特定模板
  - 定期更新和维护
- **社区模板**
  - 用户分享的角色模板
  - 评分和评论系统
  - 使用统计和推荐
- **模板管理**
  - 模板分类和筛选
  - 收藏和订阅功能
  - 模板使用指南

#### 2.1.3 版本控制
- **版本管理**
  - 自动版本保存
  - 版本历史查看
  - 版本回滚功能
  - 分支和合并
- **变更追踪**
  - 修改记录
  - 协作者信息
  - 变更对比
  - 审批流程

### 2.2 角色编辑器

#### 2.2.1 可视化编辑界面
- **结构化编辑**
  - personality（人格特征）编辑器
  - principle（行为原则）编辑器
  - thought（思维模式）编辑器
  - execution（执行模式）编辑器
- **编辑功能**
  - 富文本编辑器
  - Markdown支持
  - 代码高亮
  - 自动补全
- **预览功能**
  - 实时预览
  - 移动端预览
  - 角色效果模拟
  - 测试对话功能

#### 2.2.2 高级编辑功能
- **智能助手**
  - AI辅助编写
  - 内容建议
  - 语法优化
  - 一致性检查
- **协作编辑**
  - 多人同时编辑
  - 实时同步
  - 冲突解决
  - 评论和建议

### 2.3 MCP服务层

#### 2.3.1 MCP协议实现
- **协议支持**
  - 完整的MCP协议实现
  - 与Claude Desktop集成
  - 与其他MCP客户端兼容
  - 协议版本管理
- **服务发现**
  - 自动服务注册
  - 健康检查
  - 负载均衡
  - 故障转移

#### 2.3.2 角色激活服务
- **角色加载**
  - 动态角色加载
  - 缓存机制
  - 热更新支持
  - 性能优化
- **状态管理**
  - 会话状态跟踪
  - 角色切换
  - 上下文保持
  - 内存管理

#### 2.3.3 API接口
- **RESTful API**
  - 角色管理API
  - 用户管理API
  - 统计分析API
  - Webhook支持
- **GraphQL API**
  - 灵活的数据查询
  - 实时订阅
  - 批量操作
  - 性能优化

### 2.4 用户管理系统

#### 2.4.1 身份认证
- **注册登录**
  - 邮箱注册
  - 第三方登录（GitHub、Google）
  - 手机号验证
  - 企业SSO集成
- **安全管理**
  - 双因素认证
  - 密码策略
  - 登录日志
  - 异常检测

#### 2.4.2 权限管理
- **角色权限**
  - 管理员、编辑者、查看者
  - 自定义权限组
  - 资源级权限控制
  - 权限继承
- **团队管理**
  - 团队创建和管理
  - 成员邀请
  - 权限分配
  - 团队资源共享

#### 2.4.3 用户体验
- **个人中心**
  - 个人信息管理
  - 使用统计
  - 订阅管理
  - 偏好设置
- **通知系统**
  - 系统通知
  - 邮件通知
  - 实时推送
  - 通知偏好

## 3. 技术架构（基于Vue3）

### 3.1 前端技术栈
- **核心框架**：Vue3 + Composition API
- **开发工具**：Vite + TypeScript
- **状态管理**：Pinia
- **UI组件库**：Element Plus / Ant Design Vue
- **样式方案**：Tailwind CSS + SCSS
- **路由管理**：Vue Router 4
- **HTTP客户端**：Axios + 请求拦截器
- **代码规范**：ESLint + Prettier + Husky

### 3.2 前端架构设计
- **项目结构**
  ```
  src/
  ├── components/          # 通用组件
  │   ├── common/         # 基础组件
  │   ├── business/       # 业务组件
  │   └── layout/         # 布局组件
  ├── views/              # 页面组件
  │   ├── auth/          # 认证相关页面
  │   ├── roles/         # 角色管理页面
  │   ├── editor/        # 编辑器页面
  │   └── dashboard/     # 仪表板页面
  ├── stores/            # Pinia状态管理
  ├── composables/       # 组合式函数
  ├── utils/             # 工具函数
  ├── types/             # TypeScript类型定义
  ├── api/               # API接口
  └── assets/            # 静态资源
  ```

### 3.3 后端技术栈
- **运行环境**：Node.js 18+
- **Web框架**：Fastify + TypeScript
- **ORM框架**：Prisma
- **数据库**：PostgreSQL + Redis
- **文件存储**：MinIO (S3兼容)
- **消息队列**：RabbitMQ
- **身份认证**：JWT + Passport.js
- **API文档**：Swagger/OpenAPI
- **测试框架**：Jest + Supertest

### 3.4 系统架构
- **微服务架构**
  - 用户服务（User Service）
  - 角色服务（Role Service）
  - MCP服务（MCP Service）
  - 通知服务（Notification Service）
  - 文件服务（File Service）
- **容器化部署**
  - Docker + Docker Compose
  - Kubernetes（生产环境）
  - CI/CD：GitHub Actions
- **监控与日志**
  - Prometheus + Grafana
  - ELK Stack (Elasticsearch + Logstash + Kibana)
  - Sentry（错误监控）

## 4. 数据库设计

### 4.1 核心数据表
- **用户表（users）**
  - id, email, username, password_hash, avatar, created_at, updated_at
- **角色表（roles）**
  - id, name, description, content, category, tags, user_id, is_public, created_at, updated_at
- **版本表（role_versions）**
  - id, role_id, version, content, changelog, created_by, created_at
- **团队表（teams）**
  - id, name, description, owner_id, created_at, updated_at
- **权限表（permissions）**
  - id, user_id, team_id, role_id, permission_type, granted_at

### 4.2 Redis缓存设计
- **会话缓存**：用户登录状态、JWT令牌
- **角色缓存**：热门角色内容、模板数据
- **API缓存**：频繁查询的API响应
- **实时数据**：在线用户、协作编辑状态

## 5. 关键技术实现

### 5.1 Vue3前端关键功能

#### 5.1.1 角色编辑器组件
```typescript
// RoleEditor.vue - 核心编辑器组件
<template>
  <div class="role-editor">
    <div class="editor-sidebar">
      <RoleStructureTree :structure="roleStructure" @select="selectSection" />
    </div>
    <div class="editor-main">
      <CodeEditor 
        v-model="currentSection.content"
        :language="'markdown'"
        :options="editorOptions"
        @change="handleContentChange"
      />
    </div>
    <div class="editor-preview">
      <RolePreview :role="currentRole" />
    </div>
  </div>
</template>
```

#### 5.1.2 实时协作功能
```typescript
// useCollaboration.ts - 协作编辑组合式函数
export function useCollaboration(roleId: string) {
  const socket = useSocket()
  const collaborators = ref<User[]>([])
  const conflicts = ref<Conflict[]>([])
  
  const joinCollaboration = () => {
    socket.emit('join-role-editing', { roleId })
  }
  
  const handleContentChange = (content: string, section: string) => {
    socket.emit('content-change', { roleId, section, content })
  }
  
  return {
    collaborators,
    conflicts,
    joinCollaboration,
    handleContentChange
  }
}
```

### 5.2 MCP协议实现

#### 5.2.1 MCP服务器
```typescript
// mcp-server.ts - MCP协议服务器实现
import { Server } from '@modelcontextprotocol/sdk/server/index.js'

class PromptXMCPServer {
  private server: Server
  
  constructor() {
    this.server = new Server({
      name: 'promptx-cloud',
      version: '1.0.0'
    })
    
    this.setupHandlers()
  }
  
  private setupHandlers() {
    // 角色列表工具
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'list_roles',
          description: '获取可用的AI角色列表',
          inputSchema: {
            type: 'object',
            properties: {
              category: { type: 'string' },
              limit: { type: 'number' }
            }
          }
        },
        {
          name: 'activate_role',
          description: '激活指定的AI角色',
          inputSchema: {
            type: 'object',
            properties: {
              roleId: { type: 'string', required: true }
            }
          }
        }
      ]
    }))
    
    // 角色激活工具
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params
      
      switch (name) {
        case 'activate_role':
          return await this.activateRole(args.roleId)
        case 'list_roles':
          return await this.listRoles(args)
        default:
          throw new Error(`Unknown tool: ${name}`)
      }
    })
  }
  
  private async activateRole(roleId: string) {
    const role = await this.roleService.getRole(roleId)
    return {
      content: [
        {
          type: 'text',
          text: `角色 ${role.name} 已激活\n\n${role.content}`
        }
      ]
    }
  }
}
```

### 5.3 角色内容解析器

#### 5.3.1 PromptX格式解析
```typescript
// role-parser.ts - 角色内容解析器
interface RoleStructure {
  personality?: string
  principle?: string
  thoughts?: Record<string, string>
  executions?: Record<string, string>
}

export class RoleParser {
  static parse(content: string): RoleStructure {
    const sections: RoleStructure = {}
    
    // 解析 <role> 标签
    const roleMatch = content.match(/<role>([\s\S]*?)<\/role>/)
    if (!roleMatch) throw new Error('Invalid role format')
    
    const roleContent = roleMatch[1]
    
    // 解析 personality 部分
    const personalityMatch = roleContent.match(/<personality>([\s\S]*?)<\/personality>/)
    if (personalityMatch) {
      sections.personality = personalityMatch[1].trim()
    }
    
    // 解析 principle 部分
    const principleMatch = roleContent.match(/<principle>([\s\S]*?)<\/principle>/)
    if (principleMatch) {
      sections.principle = principleMatch[1].trim()
    }
    
    // 解析引用的 thought 和 execution
    const references = this.parseReferences(roleContent)
    sections.thoughts = references.thoughts
    sections.executions = references.executions
    
    return sections
  }
  
  static serialize(structure: RoleStructure): string {
    let content = '<role>\n'
    
    if (structure.personality) {
      content += `  <personality>\n${structure.personality}\n  </personality>\n\n`
    }
    
    if (structure.principle) {
      content += `  <principle>\n${structure.principle}\n  </principle>\n`
    }
    
    content += '</role>'
    
    return content
  }
  
  private static parseReferences(content: string) {
    const thoughts: Record<string, string> = {}
    const executions: Record<string, string> = {}
    
    // 解析 @!thought:// 引用
    const thoughtRefs = content.match(/@!thought:\/\/[\w-]+/g) || []
    // 解析 @!execution:// 引用
    const executionRefs = content.match(/@!execution:\/\/[\w-]+/g) || []
    
    // 这里需要从数据库或文件系统加载引用的内容
    
    return { thoughts, executions }
  }
}
```

## 6. 用户界面设计

### 6.1 主要页面设计

#### 6.1.1 角色管理页面
- **布局结构**
  - 左侧：角色分类树
  - 中间：角色列表（卡片式）
  - 右侧：角色详情预览
- **交互功能**
  - 拖拽排序
  - 批量操作
  - 快速搜索
  - 标签筛选

#### 6.1.2 角色编辑器页面
- **三栏布局**
  - 左侧：结构导航树
  - 中间：代码编辑器
  - 右侧：实时预览
- **编辑功能**
  - 语法高亮
  - 自动补全
  - 错误提示
  - 版本对比

#### 6.1.3 模板市场页面
- **瀑布流布局**
  - 模板卡片展示
  - 分类筛选
  - 搜索功能
  - 评分排序
- **模板详情**
  - 预览功能
  - 使用统计
  - 评论区域
  - 一键使用

### 6.2 响应式设计
- **断点设置**
  - 移动端：< 768px
  - 平板端：768px - 1024px
  - 桌面端：> 1024px
- **适配策略**
  - 移动端：单栏布局，抽屉式导航
  - 平板端：双栏布局，可折叠侧边栏
  - 桌面端：三栏布局，固定侧边栏

## 7. 开发计划与里程碑

### 7.1 MVP阶段（3个月）
- **Week 1-2**：项目初始化，技术栈搭建
- **Week 3-6**：用户系统，基础角色CRUD
- **Week 7-10**：简单编辑器，MCP协议基础实现
- **Week 11-12**：测试、部署、文档

### 7.2 V1.0阶段（6个月）
- **Month 4**：高级编辑器，语法检查
- **Month 5**：团队协作，实时编辑
- **Month 6**：模板市场，API文档

### 7.3 V2.0阶段（12个月）
- **Month 7-9**：AI辅助编写，高级分析
- **Month 10-12**：企业功能，生态集成

## 8. 质量保证

### 8.1 测试策略
- **单元测试**：Jest + Vue Test Utils
- **集成测试**：Cypress
- **API测试**：Supertest
- **性能测试**：Lighthouse + WebPageTest

### 8.2 代码质量
- **代码规范**：ESLint + Prettier
- **类型检查**：TypeScript严格模式
- **代码审查**：GitHub PR Review
- **自动化检查**：Husky + lint-staged

## 9. Vue3具体实现细节

### 9.1 核心组件设计

#### 9.1.1 角色编辑器核心组件
```vue
<!-- RoleEditor.vue -->
<template>
  <div class="role-editor h-screen flex">
    <!-- 左侧结构树 -->
    <div class="w-64 bg-gray-50 border-r">
      <RoleStructurePanel
        :structure="roleStructure"
        :active-section="activeSection"
        @section-select="handleSectionSelect"
        @section-add="handleSectionAdd"
        @section-delete="handleSectionDelete"
      />
    </div>

    <!-- 中间编辑区 -->
    <div class="flex-1 flex flex-col">
      <div class="border-b p-4">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">{{ currentRole.name }}</h2>
          <div class="flex space-x-2">
            <el-button @click="saveRole" :loading="saving">保存</el-button>
            <el-button @click="previewRole">预览</el-button>
          </div>
        </div>
      </div>

      <div class="flex-1">
        <CodeEditor
          v-model="currentContent"
          :language="'markdown'"
          :options="editorOptions"
          @change="handleContentChange"
          @cursor-change="handleCursorChange"
        />
      </div>
    </div>

    <!-- 右侧预览面板 -->
    <div class="w-80 bg-white border-l" v-if="showPreview">
      <RolePreviewPanel :role="currentRole" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useRoleStore } from '@/stores/role'
import { useCollaboration } from '@/composables/useCollaboration'
import { RoleParser } from '@/utils/role-parser'

interface Props {
  roleId?: string
}

const props = defineProps<Props>()
const route = useRoute()
const router = useRouter()
const roleStore = useRoleStore()

// 响应式数据
const currentRole = ref<Role>()
const activeSection = ref<string>('personality')
const currentContent = ref<string>('')
const saving = ref(false)
const showPreview = ref(true)

// 计算属性
const roleStructure = computed(() => {
  if (!currentRole.value) return {}
  return RoleParser.parse(currentRole.value.content)
})

const editorOptions = computed(() => ({
  theme: 'vs-dark',
  fontSize: 14,
  wordWrap: 'on',
  minimap: { enabled: false },
  scrollBeyondLastLine: false
}))

// 协作编辑
const { collaborators, joinCollaboration, leaveCollaboration } = useCollaboration(
  computed(() => props.roleId || route.params.id as string)
)

// 生命周期
onMounted(async () => {
  await loadRole()
  joinCollaboration()
})

onUnmounted(() => {
  leaveCollaboration()
})

// 方法
const loadRole = async () => {
  const roleId = props.roleId || route.params.id as string
  if (roleId) {
    currentRole.value = await roleStore.getRole(roleId)
    activeSection.value = 'personality'
    updateCurrentContent()
  }
}

const handleSectionSelect = (section: string) => {
  activeSection.value = section
  updateCurrentContent()
}

const updateCurrentContent = () => {
  if (!currentRole.value || !roleStructure.value) return

  const structure = roleStructure.value
  switch (activeSection.value) {
    case 'personality':
      currentContent.value = structure.personality || ''
      break
    case 'principle':
      currentContent.value = structure.principle || ''
      break
    default:
      if (structure.thoughts?.[activeSection.value]) {
        currentContent.value = structure.thoughts[activeSection.value]
      } else if (structure.executions?.[activeSection.value]) {
        currentContent.value = structure.executions[activeSection.value]
      }
  }
}

const handleContentChange = (content: string) => {
  currentContent.value = content
  updateRoleContent()
}

const updateRoleContent = () => {
  if (!currentRole.value) return

  const structure = { ...roleStructure.value }

  switch (activeSection.value) {
    case 'personality':
      structure.personality = currentContent.value
      break
    case 'principle':
      structure.principle = currentContent.value
      break
    default:
      if (structure.thoughts && activeSection.value in structure.thoughts) {
        structure.thoughts[activeSection.value] = currentContent.value
      } else if (structure.executions && activeSection.value in structure.executions) {
        structure.executions[activeSection.value] = currentContent.value
      }
  }

  currentRole.value.content = RoleParser.serialize(structure)
}

const saveRole = async () => {
  if (!currentRole.value) return

  saving.value = true
  try {
    await roleStore.updateRole(currentRole.value.id, {
      content: currentRole.value.content
    })
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}
</script>
```

#### 9.1.2 实时协作组合式函数
```typescript
// composables/useCollaboration.ts
import { ref, computed, onUnmounted } from 'vue'
import { useWebSocket } from '@vueuse/core'
import { useUserStore } from '@/stores/user'

export interface Collaborator {
  id: string
  name: string
  avatar: string
  cursor?: {
    line: number
    column: number
  }
  selection?: {
    start: { line: number; column: number }
    end: { line: number; column: number }
  }
}

export function useCollaboration(roleId: Ref<string>) {
  const userStore = useUserStore()
  const collaborators = ref<Collaborator[]>([])
  const isConnected = ref(false)

  const wsUrl = computed(() =>
    `${import.meta.env.VITE_WS_URL}/collaboration/${roleId.value}`
  )

  const { status, data, send, open, close } = useWebSocket(wsUrl, {
    autoReconnect: {
      retries: 3,
      delay: 1000,
      onFailed() {
        ElMessage.error('协作连接失败')
      }
    },
    heartbeat: {
      message: 'ping',
      interval: 30000
    }
  })

  // 监听连接状态
  watch(status, (newStatus) => {
    isConnected.value = newStatus === 'OPEN'
  })

  // 监听消息
  watch(data, (message) => {
    if (!message) return

    try {
      const event = JSON.parse(message)
      handleCollaborationEvent(event)
    } catch (error) {
      console.error('Failed to parse collaboration message:', error)
    }
  })

  const handleCollaborationEvent = (event: any) => {
    switch (event.type) {
      case 'user-joined':
        collaborators.value.push(event.user)
        ElMessage.info(`${event.user.name} 加入了协作`)
        break

      case 'user-left':
        collaborators.value = collaborators.value.filter(
          user => user.id !== event.userId
        )
        break

      case 'cursor-change':
        updateCollaboratorCursor(event.userId, event.cursor)
        break

      case 'content-change':
        handleRemoteContentChange(event)
        break

      case 'conflict-detected':
        handleConflict(event.conflict)
        break
    }
  }

  const joinCollaboration = () => {
    if (!roleId.value) return

    send(JSON.stringify({
      type: 'join',
      roleId: roleId.value,
      user: {
        id: userStore.user.id,
        name: userStore.user.name,
        avatar: userStore.user.avatar
      }
    }))
  }

  const leaveCollaboration = () => {
    send(JSON.stringify({
      type: 'leave',
      roleId: roleId.value
    }))
    close()
  }

  const broadcastCursorChange = (cursor: { line: number; column: number }) => {
    send(JSON.stringify({
      type: 'cursor-change',
      cursor
    }))
  }

  const broadcastContentChange = (change: any) => {
    send(JSON.stringify({
      type: 'content-change',
      change
    }))
  }

  onUnmounted(() => {
    leaveCollaboration()
  })

  return {
    collaborators: readonly(collaborators),
    isConnected: readonly(isConnected),
    joinCollaboration,
    leaveCollaboration,
    broadcastCursorChange,
    broadcastContentChange
  }
}
```

### 9.2 状态管理（Pinia）

#### 9.2.1 角色状态管理
```typescript
// stores/role.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { roleApi } from '@/api/role'

export interface Role {
  id: string
  name: string
  description: string
  content: string
  category: string
  tags: string[]
  isPublic: boolean
  userId: string
  createdAt: string
  updatedAt: string
  version: number
}

export const useRoleStore = defineStore('role', () => {
  // 状态
  const roles = ref<Role[]>([])
  const currentRole = ref<Role | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const publicRoles = computed(() =>
    roles.value.filter(role => role.isPublic)
  )

  const myRoles = computed(() =>
    roles.value.filter(role => role.userId === userStore.user?.id)
  )

  const rolesByCategory = computed(() => {
    const grouped: Record<string, Role[]> = {}
    roles.value.forEach(role => {
      if (!grouped[role.category]) {
        grouped[role.category] = []
      }
      grouped[role.category].push(role)
    })
    return grouped
  })

  // 操作
  const fetchRoles = async (params?: {
    category?: string
    tags?: string[]
    search?: string
    page?: number
    limit?: number
  }) => {
    loading.value = true
    error.value = null

    try {
      const response = await roleApi.getRoles(params)
      roles.value = response.data
      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取角色列表失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const getRole = async (id: string): Promise<Role> => {
    // 先从缓存中查找
    const cached = roles.value.find(role => role.id === id)
    if (cached) {
      currentRole.value = cached
      return cached
    }

    // 从API获取
    loading.value = true
    try {
      const role = await roleApi.getRole(id)
      currentRole.value = role

      // 更新缓存
      const index = roles.value.findIndex(r => r.id === id)
      if (index >= 0) {
        roles.value[index] = role
      } else {
        roles.value.push(role)
      }

      return role
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取角色失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const createRole = async (data: Partial<Role>): Promise<Role> => {
    loading.value = true
    try {
      const role = await roleApi.createRole(data)
      roles.value.unshift(role)
      currentRole.value = role
      return role
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建角色失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateRole = async (id: string, data: Partial<Role>): Promise<Role> => {
    loading.value = true
    try {
      const role = await roleApi.updateRole(id, data)

      // 更新缓存
      const index = roles.value.findIndex(r => r.id === id)
      if (index >= 0) {
        roles.value[index] = role
      }

      if (currentRole.value?.id === id) {
        currentRole.value = role
      }

      return role
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新角色失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteRole = async (id: string): Promise<void> => {
    loading.value = true
    try {
      await roleApi.deleteRole(id)

      // 从缓存中移除
      roles.value = roles.value.filter(role => role.id !== id)

      if (currentRole.value?.id === id) {
        currentRole.value = null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除角色失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const duplicateRole = async (id: string): Promise<Role> => {
    const original = await getRole(id)
    return createRole({
      name: `${original.name} (副本)`,
      description: original.description,
      content: original.content,
      category: original.category,
      tags: [...original.tags],
      isPublic: false
    })
  }

  const searchRoles = async (query: string): Promise<Role[]> => {
    const response = await fetchRoles({ search: query })
    return response.data
  }

  return {
    // 状态
    roles: readonly(roles),
    currentRole: readonly(currentRole),
    loading: readonly(loading),
    error: readonly(error),

    // 计算属性
    publicRoles,
    myRoles,
    rolesByCategory,

    // 操作
    fetchRoles,
    getRole,
    createRole,
    updateRole,
    deleteRole,
    duplicateRole,
    searchRoles
  }
})
```

### 9.3 API接口设计

#### 9.3.1 HTTP客户端配置
```typescript
// api/http.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

class HttpClient {
  private instance: AxiosInstance

  constructor() {
    this.instance = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const userStore = useUserStore()
        const token = userStore.token

        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }

        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response.data
      },
      (error) => {
        const { response } = error

        if (response) {
          switch (response.status) {
            case 401:
              const userStore = useUserStore()
              userStore.logout()
              ElMessage.error('登录已过期，请重新登录')
              break
            case 403:
              ElMessage.error('没有权限访问该资源')
              break
            case 404:
              ElMessage.error('请求的资源不存在')
              break
            case 500:
              ElMessage.error('服务器内部错误')
              break
            default:
              ElMessage.error(response.data?.message || '请求失败')
          }
        } else {
          ElMessage.error('网络连接失败')
        }

        return Promise.reject(error)
      }
    )
  }

  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.get(url, config)
  }

  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.post(url, data, config)
  }

  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.put(url, data, config)
  }

  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.instance.delete(url, config)
  }
}

export const http = new HttpClient()
```

#### 9.3.2 角色API接口
```typescript
// api/role.ts
import { http } from './http'
import type { Role } from '@/stores/role'

export interface RoleListParams {
  category?: string
  tags?: string[]
  search?: string
  page?: number
  limit?: number
  sortBy?: 'createdAt' | 'updatedAt' | 'name'
  sortOrder?: 'asc' | 'desc'
}

export interface RoleListResponse {
  data: Role[]
  total: number
  page: number
  limit: number
}

export const roleApi = {
  // 获取角色列表
  getRoles(params?: RoleListParams): Promise<RoleListResponse> {
    return http.get('/api/roles', { params })
  },

  // 获取单个角色
  getRole(id: string): Promise<Role> {
    return http.get(`/api/roles/${id}`)
  },

  // 创建角色
  createRole(data: Partial<Role>): Promise<Role> {
    return http.post('/api/roles', data)
  },

  // 更新角色
  updateRole(id: string, data: Partial<Role>): Promise<Role> {
    return http.put(`/api/roles/${id}`, data)
  },

  // 删除角色
  deleteRole(id: string): Promise<void> {
    return http.delete(`/api/roles/${id}`)
  },

  // 复制角色
  duplicateRole(id: string): Promise<Role> {
    return http.post(`/api/roles/${id}/duplicate`)
  },

  // 获取角色版本历史
  getRoleVersions(id: string): Promise<RoleVersion[]> {
    return http.get(`/api/roles/${id}/versions`)
  },

  // 恢复到指定版本
  restoreVersion(id: string, version: number): Promise<Role> {
    return http.post(`/api/roles/${id}/restore`, { version })
  },

  // 发布角色到市场
  publishRole(id: string): Promise<void> {
    return http.post(`/api/roles/${id}/publish`)
  },

  // 取消发布
  unpublishRole(id: string): Promise<void> {
    return http.post(`/api/roles/${id}/unpublish`)
  }
}
```

这个基于Vue3的完整技术方案为您的PromptX MCP服务提供了详细的实现指导。主要特点包括：

1. **现代化Vue3架构**：使用Composition API、TypeScript、Pinia等最新技术
2. **完整的组件设计**：包含角色编辑器、协作功能等核心组件
3. **状态管理方案**：基于Pinia的响应式状态管理
4. **API接口设计**：完整的RESTful API和HTTP客户端
5. **实时协作功能**：基于WebSocket的多人协作编辑

您希望我进一步详细展开哪个技术模块，比如MCP协议的具体实现、后端API设计，或者其他特定功能的实现细节？
