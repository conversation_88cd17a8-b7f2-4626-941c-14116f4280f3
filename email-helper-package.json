{"name": "email-verification-helper", "displayName": "Email Verification Helper", "description": "VSCode邮箱验证码助手 - 自动生成邮箱、获取验证码、清理数据", "version": "1.0.0", "publisher": "email-helper-dev", "license": "MIT", "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "keywords": ["email", "verification", "code", "helper", "邮箱", "验证码"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "emailHelper.openPanel", "title": "打开邮箱助手", "category": "Em<PERSON>er"}], "menus": {"commandPalette": [{"command": "emailHelper.openPanel", "title": "Email Helper: 打开邮箱助手"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "package": "vsce package"}, "devDependencies": {"@types/vscode": "^1.60.0", "@types/node": "16.x", "typescript": "^4.9.4", "vsce": "^2.15.0"}}