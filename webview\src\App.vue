<template>
  <div id="app" class="email-helper-app">
    <!-- 头部标题 -->
    <el-header class="app-header">
      <div class="header-content">
        <h1 class="app-title">
          <el-icon><Message /></el-icon>
          Email Verification Helper
        </h1>
        <div class="header-actions">
          <el-button @click="showSettings = true" :icon="Setting" circle />
        </div>
      </div>
    </el-header>

    <!-- 主内容区 -->
    <el-main class="app-main">
      <div class="content-container">
        <!-- 邮箱生成器 -->
        <EmailGenerator />
        
        <!-- 验证码获取器 -->
        <VerificationCodeGetter />
        
        <!-- 数据清理器 -->
        <DataCleaner />
      </div>
    </el-main>

    <!-- 设置对话框 -->
    <SettingsDialog v-model="showSettings" />
    
    <!-- 全局加载状态 -->
    <el-loading-service v-if="appStore.loading" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Message, Setting } from '@element-plus/icons-vue'
import { useAppStore } from './stores/app'
import EmailGenerator from './components/EmailGenerator.vue'
import VerificationCodeGetter from './components/VerificationCodeGetter.vue'
import DataCleaner from './components/DataCleaner.vue'
import SettingsDialog from './components/SettingsDialog.vue'

// 响应式数据
const showSettings = ref(false)

// 状态管理
const appStore = useAppStore()

// 生命周期
onMounted(() => {
  appStore.initialize()
})
</script>

<style scoped>
.email-helper-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color-page);
}

.app-header {
  border-bottom: 1px solid var(--el-border-color);
  padding: 0;
  height: 60px;
  background: var(--el-bg-color);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 20px;
}

.app-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.app-main {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.content-container {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-main {
    padding: 16px;
  }
  
  .content-container {
    gap: 16px;
  }
  
  .app-title {
    font-size: 16px;
  }
}
</style>

<style>
/* 全局样式 */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

#app {
  height: 100vh;
}

/* VSCode主题适配 */
.vscode-dark {
  --el-bg-color: #1e1e1e;
  --el-bg-color-page: #252526;
  --el-text-color-primary: #cccccc;
  --el-border-color: #3c3c3c;
}

.vscode-light {
  --el-bg-color: #ffffff;
  --el-bg-color-page: #f3f3f3;
  --el-text-color-primary: #333333;
  --el-border-color: #e4e7ed;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-text-color-secondary);
}
</style>
