const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始打包 VSCode 插件...\n');

// 检查必要的文件
const requiredFiles = [
    'package.json',
    'src/extension.ts',
    'webview/package.json'
];

console.log('📋 检查必要文件...');
for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
        console.error(`❌ 缺少必要文件: ${file}`);
        process.exit(1);
    }
    console.log(`✅ ${file}`);
}

try {
    // 1. 安装依赖
    console.log('\n📦 安装插件依赖...');
    execSync('npm install', { stdio: 'inherit' });

    // 2. 安装前端依赖
    console.log('\n📦 安装前端依赖...');
    execSync('cd webview && npm install', { stdio: 'inherit', shell: true });

    // 3. 编译插件
    console.log('\n🔨 编译插件核心...');
    execSync('npm run compile:extension', { stdio: 'inherit' });

    // 4. 编译前端
    console.log('\n🔨 编译前端应用...');
    execSync('cd webview && npm run build', { stdio: 'inherit', shell: true });

    // 5. 检查编译输出
    console.log('\n🔍 检查编译输出...');
    const outDir = 'out';
    const webviewDist = 'webview/dist';
    
    if (!fs.existsSync(outDir)) {
        console.error('❌ 插件编译失败：out 目录不存在');
        process.exit(1);
    }
    
    if (!fs.existsSync(webviewDist)) {
        console.error('❌ 前端编译失败：webview/dist 目录不存在');
        process.exit(1);
    }
    
    console.log('✅ 编译输出检查通过');

    // 6. 打包 VSIX
    console.log('\n📦 打包 VSIX 文件...');
    execSync('npx vsce package', { stdio: 'inherit' });

    // 7. 查找生成的 VSIX 文件
    const files = fs.readdirSync('.');
    const vsixFile = files.find(file => file.endsWith('.vsix'));
    
    if (vsixFile) {
        const stats = fs.statSync(vsixFile);
        const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        
        console.log('\n🎉 打包成功！');
        console.log(`📄 文件名: ${vsixFile}`);
        console.log(`📏 文件大小: ${sizeInMB} MB`);
        console.log(`📍 文件路径: ${path.resolve(vsixFile)}`);
        
        console.log('\n📋 安装说明:');
        console.log('1. 打开 VSCode');
        console.log('2. 按 Ctrl+Shift+P 打开命令面板');
        console.log('3. 输入 "Extensions: Install from VSIX..."');
        console.log(`4. 选择文件: ${vsixFile}`);
        console.log('5. 重启 VSCode 即可使用');
        
    } else {
        console.error('❌ 未找到生成的 VSIX 文件');
        process.exit(1);
    }

} catch (error) {
    console.error('\n❌ 打包过程中发生错误:');
    console.error(error.message);
    
    console.log('\n🔧 故障排除建议:');
    console.log('1. 确保 Node.js 版本 >= 18.0.0');
    console.log('2. 删除 node_modules 和 webview/node_modules，重新安装依赖');
    console.log('3. 检查 TypeScript 编译错误');
    console.log('4. 确保所有必要文件都存在');
    
    process.exit(1);
}
