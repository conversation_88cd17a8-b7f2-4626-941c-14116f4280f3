<template>
  <el-card class="data-cleaner-card">
    <template #header>
      <div class="card-header">
        <h3>
          <el-icon><Delete /></el-icon>
          数据清理器
        </h3>
      </div>
    </template>

    <div class="cleaner-content">
      <!-- 清理选项 -->
      <div class="options-section">
        <el-form :model="cleanupOptions" label-width="120px" size="default">
          <el-form-item label="清理目标">
            <el-checkbox-group v-model="selectedOptions">
              <el-checkbox label="searchHistory">
                <div class="option-item">
                  <span class="option-title">搜索历史</span>
                  <span class="option-desc">清理VSCode搜索和文件历史记录</span>
                </div>
              </el-checkbox>
              
              <el-checkbox label="workspaceHistory">
                <div class="option-item">
                  <span class="option-title">工作空间历史</span>
                  <span class="option-desc">清理工作空间存储和历史数据</span>
                </div>
              </el-checkbox>
              
              <el-checkbox label="extensionData">
                <div class="option-item">
                  <span class="option-title">扩展数据</span>
                  <span class="option-desc">清理扩展临时文件和缓存</span>
                </div>
              </el-checkbox>
              
              <el-checkbox label="cacheFiles">
                <div class="option-item">
                  <span class="option-title">缓存文件</span>
                  <span class="option-desc">清理系统缓存和临时文件</span>
                </div>
              </el-checkbox>
              
              <el-checkbox label="userConfig" disabled>
                <div class="option-item">
                  <span class="option-title">用户配置</span>
                  <span class="option-desc">清理用户设置（暂不支持，安全考虑）</span>
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>

      <!-- 预览按钮 -->
      <div class="preview-section">
        <el-button
          @click="getPreview"
          :loading="previewing"
          :icon="View"
          :disabled="selectedOptions.length === 0"
        >
          {{ previewing ? '扫描中...' : '预览清理内容' }}
        </el-button>
      </div>

      <!-- 预览结果 -->
      <div v-if="previewData" class="preview-result">
        <el-alert
          title="清理预览"
          type="info"
          :closable="false"
          show-icon
        />
        
        <el-descriptions :column="2" border class="preview-details">
          <el-descriptions-item label="搜索历史">
            {{ formatSize(previewData.searchHistory) }}
          </el-descriptions-item>
          <el-descriptions-item label="工作空间历史">
            {{ formatSize(previewData.workspaceHistory) }}
          </el-descriptions-item>
          <el-descriptions-item label="扩展数据">
            {{ formatSize(previewData.extensionData) }}
          </el-descriptions-item>
          <el-descriptions-item label="缓存文件">
            {{ formatSize(previewData.cacheFiles) }}
          </el-descriptions-item>
          <el-descriptions-item label="总计大小" span="2">
            <el-tag type="warning" size="large">
              {{ formatSize(previewData.totalSize) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 清理按钮 -->
      <div class="action-section">
        <el-button
          type="danger"
          size="large"
          :loading="cleaning"
          @click="confirmCleanup"
          :icon="Delete"
          :disabled="selectedOptions.length === 0"
          style="width: 100%"
        >
          {{ cleaning ? '清理中...' : '开始清理' }}
        </el-button>
      </div>

      <!-- 清理结果 -->
      <div v-if="cleanupResult" class="result-section">
        <el-alert
          :title="cleanupResult.success ? '清理完成' : '清理失败'"
          :type="cleanupResult.success ? 'success' : 'error'"
          :closable="false"
          show-icon
        />
        
        <div v-if="cleanupResult.success" class="success-details">
          <el-descriptions title="清理统计" :column="2" border>
            <el-descriptions-item label="清理文件数">
              {{ cleanupResult.filesDeleted }}
            </el-descriptions-item>
            <el-descriptions-item label="释放空间">
              {{ formatSize(cleanupResult.spaceFreed) }}
            </el-descriptions-item>
          </el-descriptions>
          
          <div v-if="cleanupResult.details.length > 0" class="cleanup-details">
            <h4>清理详情</h4>
            <ul>
              <li v-for="(detail, index) in cleanupResult.details" :key="index">
                {{ detail }}
              </li>
            </ul>
          </div>
        </div>
        
        <div v-if="cleanupResult.errors.length > 0" class="error-details">
          <h4>错误信息</h4>
          <ul>
            <li v-for="(error, index) in cleanupResult.errors" :key="index" class="error-item">
              {{ error }}
            </li>
          </ul>
        </div>
      </div>

      <!-- 安全提示 -->
      <div class="safety-notice">
        <el-alert
          title="安全提示"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>数据清理操作不可撤销，请谨慎操作。建议：</p>
            <ul>
              <li>清理前先预览要删除的内容</li>
              <li>重要数据请提前备份</li>
              <li>首次使用建议选择部分选项测试</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, View } from '@element-plus/icons-vue'
import { vscodeApi } from '@/utils/vscode-api'

// 响应式数据
const cleaning = ref(false)
const previewing = ref(false)
const selectedOptions = ref(['searchHistory', 'workspaceHistory', 'cacheFiles'])
const previewData = ref<any>(null)
const cleanupResult = ref<any>(null)

// 清理选项
const cleanupOptions = reactive({
  userConfig: false,
  extensionData: true,
  workspaceHistory: true,
  searchHistory: true,
  cacheFiles: true
})

// 方法
const getPreview = async () => {
  previewing.value = true
  try {
    // 这里应该调用后端API获取预览数据
    // 暂时使用模拟数据
    previewData.value = {
      searchHistory: Math.floor(Math.random() * 1000) * 1024,
      workspaceHistory: Math.floor(Math.random() * 2000) * 1024,
      extensionData: Math.floor(Math.random() * 5000) * 1024,
      cacheFiles: Math.floor(Math.random() * 3000) * 1024,
      totalSize: 0
    }
    
    previewData.value.totalSize = 
      previewData.value.searchHistory +
      previewData.value.workspaceHistory +
      previewData.value.extensionData +
      previewData.value.cacheFiles

    ElMessage.success('预览数据获取成功')
  } catch (error) {
    ElMessage.error('获取预览数据失败: ' + (error instanceof Error ? error.message : '未知错误'))
  } finally {
    previewing.value = false
  }
}

const confirmCleanup = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要执行数据清理吗？此操作不可撤销，请确保已备份重要数据。',
      '确认清理',
      {
        confirmButtonText: '确定清理',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true,
        message: `
          <p>将要清理以下数据：</p>
          <ul>
            ${selectedOptions.value.map(option => {
              const labels: Record<string, string> = {
                searchHistory: '搜索历史',
                workspaceHistory: '工作空间历史',
                extensionData: '扩展数据',
                cacheFiles: '缓存文件',
                userConfig: '用户配置'
              }
              return `<li>${labels[option] || option}</li>`
            }).join('')}
          </ul>
        `
      }
    )

    await executeCleanup()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清理确认失败')
    }
  }
}

const executeCleanup = async () => {
  cleaning.value = true
  cleanupResult.value = null

  try {
    const options = selectedOptions.value.reduce((acc, option) => {
      acc[option] = true
      return acc
    }, {} as Record<string, boolean>)

    const result = await vscodeApi.cleanupData(options)
    cleanupResult.value = result

    if (result.success) {
      ElMessage.success('数据清理完成')
      // 清理完成后重新获取预览数据
      if (previewData.value) {
        await getPreview()
      }
    } else {
      ElMessage.error('数据清理失败')
    }
  } catch (error) {
    ElMessage.error('清理过程中发生错误: ' + (error instanceof Error ? error.message : '未知错误'))
    cleanupResult.value = {
      success: false,
      filesDeleted: 0,
      spaceFreed: 0,
      errors: [error instanceof Error ? error.message : '未知错误'],
      details: []
    }
  } finally {
    cleaning.value = false
  }
}

const formatSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.data-cleaner-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-primary);
}

.cleaner-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.options-section {
  background: var(--el-bg-color-page);
  padding: 16px;
  border-radius: 6px;
}

.option-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.option-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.option-desc {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.preview-section {
  display: flex;
  justify-content: center;
}

.preview-result {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.preview-details {
  margin-top: 12px;
}

.action-section {
  display: flex;
  justify-content: center;
}

.result-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.success-details,
.error-details {
  margin-top: 12px;
}

.cleanup-details h4,
.error-details h4 {
  margin: 12px 0 8px 0;
  color: var(--el-text-color-primary);
}

.cleanup-details ul,
.error-details ul {
  margin: 0;
  padding-left: 20px;
}

.error-item {
  color: var(--el-color-danger);
}

.safety-notice {
  border-top: 1px solid var(--el-border-color);
  padding-top: 16px;
}

.safety-notice ul {
  margin: 8px 0 0 20px;
  padding: 0;
}

/* 复选框组样式 */
:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

:deep(.el-checkbox) {
  margin-right: 0;
  height: auto;
}

:deep(.el-checkbox__label) {
  padding-left: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-details {
    font-size: 14px;
  }
  
  .option-item {
    gap: 2px;
  }
  
  .option-desc {
    font-size: 11px;
  }
}
</style>
