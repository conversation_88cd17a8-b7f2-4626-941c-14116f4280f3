<template>
  <el-card class="verification-code-card">
    <template #header>
      <div class="card-header">
        <h3>
          <el-icon><Key /></el-icon>
          验证码获取器
        </h3>
      </div>
    </template>

    <div class="getter-content">
      <!-- 配置区域 -->
      <div class="config-section">
        <el-form :model="form" label-width="100px" size="default">
          <el-form-item label="监控邮箱">
            <el-input
              v-model="form.email"
              placeholder="输入要监控的邮箱地址"
              clearable
            >
              <template #prepend>
                <el-icon><Message /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="关键词">
            <el-select
              v-model="form.keywords"
              multiple
              filterable
              allow-create
              placeholder="选择或输入关键词"
              style="width: 100%"
            >
              <el-option
                v-for="keyword in defaultKeywords"
                :key="keyword"
                :label="keyword"
                :value="keyword"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 获取按钮 -->
      <div class="action-section">
        <el-button
          type="primary"
          size="large"
          :loading="fetching"
          @click="getVerificationCode"
          :icon="Search"
          :disabled="!form.email"
          style="width: 100%"
        >
          {{ fetching ? '获取中...' : '获取验证码' }}
        </el-button>
      </div>

      <!-- 结果显示 -->
      <div v-if="verificationCode" class="result-section">
        <el-alert
          title="验证码获取成功"
          type="success"
          :closable="false"
          show-icon
        />
        
        <div class="code-result">
          <el-input
            v-model="verificationCode"
            readonly
            size="large"
            class="code-input"
          >
            <template #prepend>
              <span>验证码</span>
            </template>
            <template #append>
              <el-button @click="copyCode" :icon="CopyDocument">
                复制
              </el-button>
            </template>
          </el-input>
        </div>

        <div class="code-info">
          <el-descriptions :column="1" size="small" border>
            <el-descriptions-item label="获取时间">
              {{ formatTime(codeInfo.receivedAt) }}
            </el-descriptions-item>
            <el-descriptions-item label="邮件主题">
              {{ codeInfo.subject || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="来源邮箱">
              {{ codeInfo.email || form.email }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 无结果提示 -->
      <div v-if="showNoResult" class="no-result-section">
        <el-alert
          title="未找到验证码"
          type="warning"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>在指定邮箱中未找到包含关键词的验证码邮件。</p>
            <p>请检查：</p>
            <ul>
              <li>邮箱地址是否正确</li>
              <li>邮件是否已到达</li>
              <li>关键词设置是否合适</li>
            </ul>
          </template>
        </el-alert>
      </div>

      <!-- 自动检查设置 -->
      <div class="auto-check-section">
        <el-divider content-position="left">自动检查设置</el-divider>
        
        <div class="auto-check-controls">
          <el-switch
            v-model="autoCheck"
            active-text="启用自动检查"
            inactive-text="关闭自动检查"
            @change="toggleAutoCheck"
          />
          
          <div v-if="autoCheck" class="interval-setting">
            <el-text>检查间隔：</el-text>
            <el-input-number
              v-model="checkInterval"
              :min="10"
              :max="300"
              :step="10"
              size="small"
              @change="updateCheckInterval"
            />
            <el-text>秒</el-text>
          </div>
        </div>

        <div v-if="autoCheck" class="auto-check-status">
          <el-tag :type="autoCheckRunning ? 'success' : 'info'">
            {{ autoCheckRunning ? '自动检查运行中' : '自动检查已停止' }}
          </el-tag>
          <el-text size="small" type="info">
            下次检查: {{ nextCheckTime }}
          </el-text>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Key, Message, Search, CopyDocument } from '@element-plus/icons-vue'
import { vscodeApi } from '@/utils/vscode-api'
import { useAppStore } from '@/stores/app'

// 状态管理
const appStore = useAppStore()

// 响应式数据
const fetching = ref(false)
const verificationCode = ref('')
const showNoResult = ref(false)
const autoCheck = ref(false)
const autoCheckRunning = ref(false)
const checkInterval = ref(30)
const nextCheckTime = ref('')

// 表单数据
const form = reactive({
  email: '',
  keywords: ['验证码', 'verification', 'code']
})

// 验证码信息
const codeInfo = reactive({
  receivedAt: new Date(),
  subject: '',
  email: ''
})

// 默认关键词
const defaultKeywords = ref([
  '验证码',
  'verification',
  'code',
  '验证',
  'verify',
  'OTP',
  '动态密码',
  'security code'
])

// 自动检查定时器
let autoCheckTimer: NodeJS.Timeout | null = null

// 生命周期
onMounted(() => {
  loadConfig()
  loadSettings()
})

onUnmounted(() => {
  if (autoCheckTimer) {
    clearInterval(autoCheckTimer)
  }
})

// 方法
const loadConfig = () => {
  if (appStore.config) {
    form.keywords = [...appStore.config.extractionKeywords]
    checkInterval.value = appStore.config.checkInterval
  }
}

const loadSettings = () => {
  // 从本地存储加载设置
  const saved = localStorage.getItem('verificationCodeSettings')
  if (saved) {
    try {
      const settings = JSON.parse(saved)
      form.email = settings.email || ''
      form.keywords = settings.keywords || form.keywords
      autoCheck.value = settings.autoCheck || false
      checkInterval.value = settings.checkInterval || 30
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  }
}

const saveSettings = () => {
  const settings = {
    email: form.email,
    keywords: form.keywords,
    autoCheck: autoCheck.value,
    checkInterval: checkInterval.value
  }
  localStorage.setItem('verificationCodeSettings', JSON.stringify(settings))
}

const getVerificationCode = async () => {
  if (!form.email) {
    ElMessage.warning('请输入邮箱地址')
    return
  }

  fetching.value = true
  showNoResult.value = false
  verificationCode.value = ''

  try {
    const code = await vscodeApi.getVerificationCode(form.email)
    
    if (code) {
      verificationCode.value = code
      codeInfo.receivedAt = new Date()
      codeInfo.email = form.email
      codeInfo.subject = '验证码邮件' // 实际应该从邮件中获取
      
      ElMessage.success('验证码获取成功')
    } else {
      showNoResult.value = true
      ElMessage.warning('未找到验证码')
    }
  } catch (error) {
    ElMessage.error('获取验证码失败: ' + (error instanceof Error ? error.message : '未知错误'))
    showNoResult.value = true
  } finally {
    fetching.value = false
    saveSettings()
  }
}

const copyCode = async () => {
  if (!verificationCode.value) return

  try {
    await vscodeApi.copyToClipboard(verificationCode.value)
    ElMessage.success('验证码已复制到剪贴板')
  } catch (error) {
    // 降级到浏览器API
    try {
      await navigator.clipboard.writeText(verificationCode.value)
      ElMessage.success('验证码已复制到剪贴板')
    } catch (fallbackError) {
      ElMessage.error('复制失败')
    }
  }
}

const toggleAutoCheck = (enabled: boolean) => {
  if (enabled) {
    startAutoCheck()
  } else {
    stopAutoCheck()
  }
  saveSettings()
}

const startAutoCheck = () => {
  if (!form.email) {
    ElMessage.warning('请先输入邮箱地址')
    autoCheck.value = false
    return
  }

  autoCheckRunning.value = true
  updateNextCheckTime()

  autoCheckTimer = setInterval(async () => {
    try {
      await getVerificationCode()
      updateNextCheckTime()
    } catch (error) {
      console.error('自动检查失败:', error)
    }
  }, checkInterval.value * 1000)

  ElMessage.success('自动检查已启动')
}

const stopAutoCheck = () => {
  if (autoCheckTimer) {
    clearInterval(autoCheckTimer)
    autoCheckTimer = null
  }
  autoCheckRunning.value = false
  nextCheckTime.value = ''
  ElMessage.info('自动检查已停止')
}

const updateCheckInterval = (newInterval: number) => {
  checkInterval.value = newInterval
  if (autoCheckRunning.value) {
    stopAutoCheck()
    startAutoCheck()
  }
  saveSettings()
}

const updateNextCheckTime = () => {
  const next = new Date(Date.now() + checkInterval.value * 1000)
  nextCheckTime.value = next.toLocaleTimeString()
}

const formatTime = (date: Date) => {
  return date.toLocaleString()
}
</script>

<style scoped>
.verification-code-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--el-text-color-primary);
}

.getter-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.config-section {
  background: var(--el-bg-color-page);
  padding: 16px;
  border-radius: 6px;
}

.action-section {
  display: flex;
  justify-content: center;
}

.result-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.code-result {
  margin-top: 12px;
}

.code-input :deep(.el-input__inner) {
  font-family: monospace;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  letter-spacing: 2px;
}

.code-info {
  margin-top: 12px;
}

.no-result-section {
  margin-top: 12px;
}

.no-result-section ul {
  margin: 8px 0 0 20px;
  padding: 0;
}

.auto-check-section {
  border-top: 1px solid var(--el-border-color);
  padding-top: 16px;
}

.auto-check-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 12px;
}

.interval-setting {
  display: flex;
  align-items: center;
  gap: 8px;
}

.auto-check-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: var(--el-bg-color-page);
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auto-check-controls {
    align-items: flex-start;
  }
  
  .interval-setting {
    flex-wrap: wrap;
  }
}
</style>
