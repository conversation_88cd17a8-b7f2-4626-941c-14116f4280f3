import * as vscode from 'vscode';

export interface EmailHelperConfig {
    defaultEmailSuffix: string;
    usernameLength: number;
    historyLimit: number;
    checkInterval: number;
    extractionKeywords: string[];
    emailServices: EmailServiceConfig[];
    imapConfig?: IMAPConfig;
}

export interface EmailServiceConfig {
    name: string;
    domain: string;
    imap?: IMAPConfig;
}

export interface IMAPConfig {
    host: string;
    port: number;
    secure: boolean;
    username?: string;
    password?: string;
}

export class ConfigService {
    private config: EmailHelperConfig;

    constructor() {
        this.config = this.loadConfig();
    }

    /**
     * 获取配置
     */
    getConfig(): EmailHelperConfig {
        return { ...this.config };
    }

    /**
     * 更新配置
     */
    async updateConfig(newConfig: Partial<EmailHelperConfig>): Promise<void> {
        const workspaceConfig = vscode.workspace.getConfiguration('emailHelper');
        
        for (const [key, value] of Object.entries(newConfig)) {
            if (key in this.config) {
                await workspaceConfig.update(key, value, vscode.ConfigurationTarget.Global);
                (this.config as any)[key] = value;
            }
        }
    }

    /**
     * 重新加载配置
     */
    reloadConfig(): void {
        this.config = this.loadConfig();
    }

    /**
     * 获取邮箱后缀列表
     */
    getEmailSuffixes(): string[] {
        return this.config.emailServices.map(service => service.domain);
    }

    /**
     * 获取邮箱服务配置
     */
    getEmailServiceConfig(domain: string): EmailServiceConfig | undefined {
        return this.config.emailServices.find(service => service.domain === domain);
    }

    /**
     * 添加邮箱服务配置
     */
    async addEmailService(serviceConfig: EmailServiceConfig): Promise<void> {
        const existingIndex = this.config.emailServices.findIndex(
            service => service.domain === serviceConfig.domain
        );

        if (existingIndex >= 0) {
            this.config.emailServices[existingIndex] = serviceConfig;
        } else {
            this.config.emailServices.push(serviceConfig);
        }

        await this.updateConfig({ emailServices: this.config.emailServices });
    }

    /**
     * 删除邮箱服务配置
     */
    async removeEmailService(domain: string): Promise<void> {
        this.config.emailServices = this.config.emailServices.filter(
            service => service.domain !== domain
        );
        await this.updateConfig({ emailServices: this.config.emailServices });
    }

    /**
     * 验证配置
     */
    validateConfig(): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        // 验证邮箱后缀
        if (!this.config.defaultEmailSuffix || !this.config.defaultEmailSuffix.startsWith('@')) {
            errors.push('默认邮箱后缀必须以@开头');
        }

        // 验证用户名长度
        if (this.config.usernameLength < 4 || this.config.usernameLength > 20) {
            errors.push('用户名长度必须在4-20之间');
        }

        // 验证历史记录限制
        if (this.config.historyLimit < 1 || this.config.historyLimit > 100) {
            errors.push('历史记录限制必须在1-100之间');
        }

        // 验证检查间隔
        if (this.config.checkInterval < 10 || this.config.checkInterval > 3600) {
            errors.push('检查间隔必须在10-3600秒之间');
        }

        // 验证关键词
        if (!this.config.extractionKeywords || this.config.extractionKeywords.length === 0) {
            errors.push('至少需要一个提取关键词');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 重置为默认配置
     */
    async resetToDefault(): Promise<void> {
        const defaultConfig = this.getDefaultConfig();
        await this.updateConfig(defaultConfig);
        this.config = defaultConfig;
    }

    /**
     * 加载配置
     */
    private loadConfig(): EmailHelperConfig {
        const workspaceConfig = vscode.workspace.getConfiguration('emailHelper');
        
        return {
            defaultEmailSuffix: workspaceConfig.get('defaultEmailSuffix', '@temp-mail.org'),
            usernameLength: workspaceConfig.get('usernameLength', 8),
            historyLimit: workspaceConfig.get('historyLimit', 10),
            checkInterval: workspaceConfig.get('checkInterval', 30),
            extractionKeywords: workspaceConfig.get('extractionKeywords', ['验证码', 'verification', 'code']),
            emailServices: this.getDefaultEmailServices(),
            imapConfig: undefined
        };
    }

    /**
     * 获取默认配置
     */
    private getDefaultConfig(): EmailHelperConfig {
        return {
            defaultEmailSuffix: '@temp-mail.org',
            usernameLength: 8,
            historyLimit: 10,
            checkInterval: 30,
            extractionKeywords: ['验证码', 'verification', 'code'],
            emailServices: this.getDefaultEmailServices(),
            imapConfig: undefined
        };
    }

    /**
     * 获取默认邮箱服务配置
     */
    private getDefaultEmailServices(): EmailServiceConfig[] {
        return [
            {
                name: 'TempMail',
                domain: '@temp-mail.org',
                imap: {
                    host: 'imap.temp-mail.org',
                    port: 993,
                    secure: true
                }
            },
            {
                name: '10MinuteMail',
                domain: '@10minutemail.com',
                imap: {
                    host: 'imap.10minutemail.com',
                    port: 993,
                    secure: true
                }
            },
            {
                name: 'Guerrilla Mail',
                domain: '@guerrillamail.com',
                imap: {
                    host: 'imap.guerrillamail.com',
                    port: 993,
                    secure: true
                }
            }
        ];
    }

    /**
     * 导出配置
     */
    exportConfig(): string {
        return JSON.stringify(this.config, null, 2);
    }

    /**
     * 导入配置
     */
    async importConfig(configJson: string): Promise<void> {
        try {
            const importedConfig = JSON.parse(configJson) as Partial<EmailHelperConfig>;
            
            // 验证导入的配置
            const tempConfig = { ...this.config, ...importedConfig };
            const validation = this.validateConfig();
            
            if (!validation.isValid) {
                throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
            }

            await this.updateConfig(importedConfig);
        } catch (error) {
            throw new Error(`导入配置失败: ${error}`);
        }
    }
}
