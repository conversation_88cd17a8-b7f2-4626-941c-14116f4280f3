<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小红书智能体 - 完整原型展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 25%, #ff9ff3 50%, #54a0ff 75%, #5f27cd 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            color: #333;
            line-height: 1.6;
            padding: 40px 20px;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .main-title {
            text-align: center;
            color: white;
            font-size: 42px;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            letter-spacing: 2px;
        }

        .subtitle {
            text-align: center;
            color: rgba(255,255,255,0.9);
            font-size: 18px;
            margin-bottom: 60px;
            font-weight: 300;
        }

        .page-section {
            margin-bottom: 100px;
            position: relative;
        }

        .section-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .section-title {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            color: white;
            font-size: 28px;
            font-weight: 600;
            padding: 20px 40px;
            border-radius: 50px;
            display: inline-block;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .phone-container {
            display: flex;
            flex-wrap: wrap;
            gap: 40px;
            justify-content: center;
            align-items: flex-start;
        }

        .phone-frame {
            width: 375px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.3);
            overflow: hidden;
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-top: 50px;
        }

        .phone-frame:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.4);
        }

        .phone-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 0;
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            position: relative;
        }

        .phone-header::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
        }

        .page-content {
            padding: 30px 25px;
            height: 600px;
            background: white;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .page-content::-webkit-scrollbar {
            width: 4px;
        }

        .page-content::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.1);
            border-radius: 2px;
        }

        .page-content::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.3);
            border-radius: 2px;
        }

        .page-content::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.5);
        }

        .page-label {
            position: absolute;
            top: -45px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
            z-index: 10;
            white-space: nowrap;
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f8f9fa;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 700;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .back-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }

        .card {
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.9) 100%);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 15px;
        }

        .input-group input,
        .input-group select,
        .input-group textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.9);
            font-size: 15px;
            transition: all 0.3s ease;
        }

        .input-group input:focus,
        .input-group select:focus,
        .input-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 2px solid rgba(102, 126, 234, 0.2);
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-1px);
        }

        .nav-tabs {
            display: flex;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            padding: 6px;
            margin-bottom: 25px;
        }

        .nav-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            border-radius: 10px;
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .grid-item {
            background: rgba(255, 255, 255, 0.8);
            padding: 18px;
            border-radius: 12px;
            text-align: center;
            border: 2px solid #e9ecef;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .grid-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        }

        .grid-item.selected {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
            border-color: #667eea;
            color: #667eea;
        }

        .member-badge {
            background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
            color: #333;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
        }

        .points-display {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
            color: #667eea;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            display: flex;
            padding: 15px 0;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            color: #666;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-item.active {
            color: #667eea;
        }

        .nav-item:hover {
            color: #667eea;
        }

        .list-item {
            border-bottom: 1px solid rgba(0,0,0,0.1);
            padding: 20px 0;
            transition: all 0.3s ease;
        }

        .list-item:hover {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 8px;
            margin: 0 -10px;
            padding: 20px 10px;
        }

        .list-item:last-child {
            border-bottom: none;
        }

        .section-divider {
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.6) 50%, transparent 100%);
            margin: 80px 0;
            border-radius: 2px;
        }

        .feature-highlight {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
            margin-left: 8px;
        }

        @media (max-width: 768px) {
            .phone-container {
                flex-direction: column;
                align-items: center;
            }

            .phone-frame {
                width: 100%;
                max-width: 375px;
            }

            .main-title {
                font-size: 32px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="main-title">小红书智能体 H5 原型</h1>
        <p class="subtitle">完整功能展示 · 逻辑清晰 · 美观大方</p>

        <!-- 1. 登录注册系统 -->
        <div class="page-section">
            <div class="section-header">
                <div class="section-title">🔐 登录注册系统</div>
            </div>

            <div class="phone-container">
                <!-- 登录页面 -->
                <div class="phone-frame">
                    <div class="page-label">登录页面</div>
                    <div class="phone-header">小红书智能体 · 登录</div>
                    <div class="page-content">
                        <div class="header">
                            <h1>欢迎回来</h1>
                        </div>

                        <div class="card">
                            <div class="input-group">
                                <label>手机号</label>
                                <input type="tel" placeholder="请输入手机号" value="138****8888">
                            </div>

                            <div class="input-group">
                                <label>密码</label>
                                <input type="password" placeholder="请输入密码" value="••••••••">
                            </div>

                            <div class="input-group">
                                <label>图形验证码</label>
                                <div style="display: flex; gap: 12px; align-items: center;">
                                    <input type="text" placeholder="请输入验证码" style="flex: 1;" value="AB3C">
                                    <div style="width: 80px; height: 40px; background: linear-gradient(45deg, #667eea, #764ba2); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 14px;">AB3C</div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: space-between; align-items: center; margin: 20px 0;">
                                <label style="display: flex; align-items: center; font-size: 14px; cursor: pointer;">
                                    <input type="checkbox" style="margin-right: 8px;" checked> 记住登录状态
                                </label>
                                <a href="#" style="color: #667eea; text-decoration: none; font-size: 14px;">忘记密码？</a>
                            </div>

                            <button class="btn-primary">登录</button>

                            <div style="text-align: center; margin-top: 20px;">
                                <span style="color: #666; font-size: 14px;">还没有账号？</span>
                                <a href="#" style="color: #667eea; text-decoration: none; font-weight: 500;">立即注册</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 注册页面 -->
                <div class="phone-frame">
                    <div class="page-label">注册页面</div>
                    <div class="phone-header">小红书智能体 · 注册</div>
                    <div class="page-content">
                        <div class="header">
                            <button class="back-btn">← 返回</button>
                            <h1>创建账户</h1>
                        </div>

                        <div class="card">
                            <div class="input-group">
                                <label>用户名</label>
                                <input type="text" placeholder="请输入用户名">
                            </div>

                            <div class="input-group">
                                <label>手机号</label>
                                <input type="tel" placeholder="请输入手机号">
                            </div>

                            <div class="input-group">
                                <label>手机验证码</label>
                                <div style="display: flex; gap: 12px;">
                                    <input type="text" placeholder="请输入验证码" style="flex: 1;">
                                    <button class="btn-secondary">获取验证码</button>
                                </div>
                            </div>

                            <div class="input-group">
                                <label>图形验证码</label>
                                <div style="display: flex; gap: 12px; align-items: center;">
                                    <input type="text" placeholder="请输入验证码" style="flex: 1;">
                                    <div style="width: 80px; height: 40px; background: linear-gradient(45deg, #ff6b6b, #ee5a24); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 14px;">XY9Z</div>
                                </div>
                            </div>

                            <div class="input-group">
                                <label>密码</label>
                                <input type="password" placeholder="请设置密码">
                            </div>

                            <div class="input-group">
                                <label>确认密码</label>
                                <input type="password" placeholder="请再次输入密码">
                            </div>

                            <div style="margin: 20px 0;">
                                <label style="display: flex; align-items: flex-start; font-size: 14px; cursor: pointer;">
                                    <input type="checkbox" style="margin-right: 8px; margin-top: 2px;">
                                    <span>我已阅读并同意<a href="#" style="color: #667eea;">《用户协议》</a>和<a href="#" style="color: #667eea;">《隐私政策》</a></span>
                                </label>
                            </div>

                            <button class="btn-primary">注册</button>
                        </div>
                    </div>
                </div>

                <!-- 密码修改页面 -->
                <div class="phone-frame">
                    <div class="page-label">密码修改</div>
                    <div class="phone-header">小红书智能体 · 安全</div>
                    <div class="page-content">
                        <div class="header">
                            <button class="back-btn">← 返回</button>
                            <h1>修改密码</h1>
                        </div>

                        <div class="nav-tabs">
                            <div class="nav-tab active">原密码验证</div>
                            <div class="nav-tab">手机验证码</div>
                        </div>

                        <div class="card">
                            <div class="input-group">
                                <label>原密码</label>
                                <input type="password" placeholder="请输入原密码">
                            </div>

                            <div class="input-group">
                                <label>新密码</label>
                                <input type="password" placeholder="请输入新密码">
                            </div>

                            <div class="input-group">
                                <label>确认新密码</label>
                                <input type="password" placeholder="请再次输入新密码">
                            </div>

                            <button class="btn-primary">确认修改</button>
                        </div>

                        <div class="card">
                            <h3 style="margin-bottom: 15px; color: #2c3e50;">🔒 账户安全提示</h3>
                            <div style="font-size: 14px; color: #666; line-height: 1.6;">
                                <p>• 密码长度至少8位，包含字母和数字</p>
                                <p>• 定期更换密码，保护账户安全</p>
                                <p>• 不要在公共场所输入密码</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 2. 主页功能 -->
        <div class="page-section">
            <div class="section-header">
                <div class="section-title">🏠 主页功能</div>
            </div>

            <div class="phone-container">
                <!-- 爆款文案生成页面 -->
                <div class="phone-frame">
                    <div class="page-label">文案生成</div>
                    <div class="phone-header">小红书智能体 · 主页</div>
                    <div class="page-content">
                        <div class="header">
                            <h1>智能文案</h1>
                            <div style="display: flex; gap: 8px; align-items: center;">
                                <div class="member-badge">VIP</div>
                                <div class="points-display">1280积分</div>
                            </div>
                        </div>

                        <div class="nav-tabs">
                            <div class="nav-tab active">文案生成</div>
                            <div class="nav-tab">素材库</div>
                            <div class="nav-tab">定制场景</div>
                        </div>

                        <div class="card">
                            <h3 style="margin-bottom: 20px; color: #2c3e50;">🎯 场景设置</h3>

                            <div class="input-group">
                                <label>行业选择</label>
                                <select>
                                    <option>美妆护肤</option>
                                    <option>服装时尚</option>
                                    <option>美食餐饮</option>
                                    <option>旅游出行</option>
                                    <option>数码科技</option>
                                    <option>家居生活</option>
                                </select>
                            </div>

                            <div class="input-group">
                                <label>受众人群 <span style="color: #666; font-size: 12px;">(最多选择2个)</span></label>
                                <div class="grid">
                                    <div class="grid-item selected">18-25岁女性</div>
                                    <div class="grid-item">26-35岁女性</div>
                                    <div class="grid-item selected">学生群体</div>
                                    <div class="grid-item">职场白领</div>
                                    <div class="grid-item">宝妈群体</div>
                                    <div class="grid-item">时尚达人</div>
                                </div>
                            </div>

                            <div class="input-group">
                                <label>发布人身份</label>
                                <div class="grid">
                                    <div class="grid-item">品牌官方</div>
                                    <div class="grid-item selected">KOL达人</div>
                                    <div class="grid-item">普通用户</div>
                                    <div class="grid-item">专业测评</div>
                                </div>
                            </div>

                            <div class="input-group">
                                <label>产品卖点</label>
                                <textarea rows="3" placeholder="请详细描述产品的核心卖点和特色...">这款面霜含有玻尿酸和烟酰胺成分，能够深层补水保湿，改善肌肤暗沉，适合干性和混合性肌肤使用...</textarea>
                            </div>
                        </div>

                        <div class="card">
                            <h3 style="margin-bottom: 20px; color: #2c3e50;">✨ 个性化设置</h3>

                            <div class="input-group">
                                <label>选择素材</label>
                                <div style="display: flex; gap: 12px;">
                                    <button class="btn-secondary">📷 选择图片</button>
                                    <button class="btn-secondary">🎥 选择视频</button>
                                </div>
                            </div>

                            <div class="input-group">
                                <label>话题标签</label>
                                <div style="margin-bottom: 12px;">
                                    <span style="background: rgba(102, 126, 234, 0.1); padding: 6px 12px; border-radius: 15px; font-size: 12px; margin-right: 8px; color: #667eea;">#美妆种草</span>
                                    <span style="background: rgba(102, 126, 234, 0.1); padding: 6px 12px; border-radius: 15px; font-size: 12px; margin-right: 8px; color: #667eea;">#护肤心得</span>
                                    <span class="feature-highlight">HOT</span>
                                </div>
                                <input type="text" placeholder="添加自定义话题..." value="#干皮救星">
                            </div>

                            <button class="btn-primary">🚀 生成爆款文案 (消耗20积分)</button>
                        </div>

                        <div class="card" style="background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);">
                            <h3 style="margin-bottom: 15px; color: #2c3e50;">📝 生成结果</h3>
                            <div style="background: white; padding: 15px; border-radius: 12px; border-left: 4px solid #667eea;">
                                <p style="line-height: 1.6; color: #333;">
                                    "姐妹们！这款面霜真的太好用了！🥰 用了一周皮肤明显变嫩变亮，玻尿酸+烟酰胺的组合真的绝了！干皮姐妹必入！ #美妆种草 #护肤心得 #干皮救星"
                                </p>
                            </div>
                            <div style="display: flex; gap: 10px; margin-top: 15px;">
                                <button class="btn-secondary">✏️ 编辑</button>
                                <button class="btn-secondary">💾 保存</button>
                                <button class="btn-secondary">📤 分享</button>
                                <button class="btn-secondary">🔄 重新生成</button>
                            </div>
                        </div>
                    </div>

                    <div class="bottom-nav">
                        <div class="nav-item active">
                            <div style="font-size: 20px; margin-bottom: 4px;">🏠</div>
                            <div>首页</div>
                        </div>
                        <div class="nav-item">
                            <div style="font-size: 20px; margin-bottom: 4px;">📝</div>
                            <div>记录</div>
                        </div>
                        <div class="nav-item">
                            <div style="font-size: 20px; margin-bottom: 4px;">💎</div>
                            <div>充值</div>
                        </div>
                        <div class="nav-item">
                            <div style="font-size: 20px; margin-bottom: 4px;">👤</div>
                            <div>我的</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 3. 个人中心 -->
        <div class="page-section">
            <div class="section-header">
                <div class="section-title">👤 个人中心</div>
            </div>

            <div class="phone-container">
                <!-- 个人中心主页 -->
                <div class="phone-frame">
                    <div class="page-label">个人中心</div>
                    <div class="phone-header">小红书智能体 · 我的</div>
                    <div class="page-content">
                        <div class="header">
                            <h1>个人中心</h1>
                        </div>

                        <div class="card" style="text-align: center; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);">
                            <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; color: white; font-size: 32px; font-weight: 600;">U</div>
                            <div style="font-size: 20px; font-weight: 600; color: #2c3e50; margin-bottom: 5px;">用户138****8888</div>
                            <div style="display: flex; gap: 15px; justify-content: center; align-items: center;">
                                <div class="member-badge">VIP会员</div>
                                <div class="points-display">1280积分</div>
                            </div>
                        </div>

                        <div class="card">
                            <h3 style="margin-bottom: 20px; color: #2c3e50;">📊 我的数据</h3>

                            <div class="grid">
                                <div style="text-align: center; padding: 20px; background: rgba(102, 126, 234, 0.05); border-radius: 12px;">
                                    <div style="font-size: 24px; font-weight: 700; color: #667eea; margin-bottom: 5px;">156</div>
                                    <div style="font-size: 12px; color: #666;">生成文案</div>
                                </div>
                                <div style="text-align: center; padding: 20px; background: rgba(255, 107, 107, 0.05); border-radius: 12px;">
                                    <div style="font-size: 24px; font-weight: 700; color: #ff6b6b; margin-bottom: 5px;">24</div>
                                    <div style="font-size: 12px; color: #666;">保存素材</div>
                                </div>
                                <div style="text-align: center; padding: 20px; background: rgba(85, 230, 193, 0.05); border-radius: 12px;">
                                    <div style="font-size: 24px; font-weight: 700; color: #55e6c1; margin-bottom: 5px;">8</div>
                                    <div style="font-size: 12px; color: #666;">场景模板</div>
                                </div>
                                <div style="text-align: center; padding: 20px; background: rgba(255, 159, 67, 0.05); border-radius: 12px;">
                                    <div style="font-size: 24px; font-weight: 700; color: #ff9f43; margin-bottom: 5px;">32</div>
                                    <div style="font-size: 12px; color: #666;">使用天数</div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3 style="margin-bottom: 20px; color: #2c3e50;">⚙️ 账户管理</h3>

                            <div class="list-item">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; align-items: center;">
                                        <div style="font-size: 20px; margin-right: 12px;">👤</div>
                                        <div>个人信息</div>
                                    </div>
                                    <div style="color: #667eea;">→</div>
                                </div>
                            </div>

                            <div class="list-item">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; align-items: center;">
                                        <div style="font-size: 20px; margin-right: 12px;">🔒</div>
                                        <div>修改密码</div>
                                    </div>
                                    <div style="color: #667eea;">→</div>
                                </div>
                            </div>

                            <div class="list-item">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; align-items: center;">
                                        <div style="font-size: 20px; margin-right: 12px;">💎</div>
                                        <div>积分充值</div>
                                    </div>
                                    <div style="color: #667eea;">→</div>
                                </div>
                            </div>

                            <div class="list-item">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; align-items: center;">
                                        <div style="font-size: 20px; margin-right: 12px;">👑</div>
                                        <div>会员中心</div>
                                    </div>
                                    <div style="color: #667eea;">→</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div style="font-size: 20px; margin-bottom: 4px;">🏠</div>
                            <div>首页</div>
                        </div>
                        <div class="nav-item">
                            <div style="font-size: 20px; margin-bottom: 4px;">📝</div>
                            <div>记录</div>
                        </div>
                        <div class="nav-item">
                            <div style="font-size: 20px; margin-bottom: 4px;">💎</div>
                            <div>充值</div>
                        </div>
                        <div class="nav-item active">
                            <div style="font-size: 20px; margin-bottom: 4px;">👤</div>
                            <div>我的</div>
                        </div>
                    </div>
                </div>

                <!-- 积分充值页面 -->
                <div class="phone-frame">
                    <div class="page-label">积分充值</div>
                    <div class="phone-header">小红书智能体 · 充值</div>
                    <div class="page-content">
                        <div class="header">
                            <button class="back-btn">← 返回</button>
                            <h1>积分充值</h1>
                        </div>

                        <div class="card" style="text-align: center; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);">
                            <div style="font-size: 32px; margin-bottom: 10px;">💎</div>
                            <div style="font-size: 18px; color: #666; margin-bottom: 8px;">当前积分</div>
                            <div style="font-size: 36px; font-weight: 700; color: #667eea;">1,280</div>
                        </div>

                        <div class="card">
                            <h3 style="margin-bottom: 20px; color: #2c3e50;">💰 选择充值套餐</h3>

                            <div class="grid-item" style="margin-bottom: 15px; border: 2px solid transparent; cursor: pointer;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="font-weight: 600; font-size: 16px;">基础套餐</div>
                                        <div style="font-size: 12px; color: #666;">500积分</div>
                                    </div>
                                    <div style="font-weight: 700; color: #667eea; font-size: 18px;">¥29</div>
                                </div>
                            </div>

                            <div class="grid-item selected" style="margin-bottom: 15px; border: 2px solid #667eea; cursor: pointer;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="font-weight: 600; font-size: 16px;">推荐套餐 <span class="feature-highlight">HOT</span></div>
                                        <div style="font-size: 12px; color: #666;">1200积分 + 200赠送</div>
                                    </div>
                                    <div style="font-weight: 700; color: #667eea; font-size: 18px;">¥69</div>
                                </div>
                            </div>

                            <div class="grid-item" style="margin-bottom: 15px; border: 2px solid transparent; cursor: pointer;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="font-weight: 600; font-size: 16px;">超值套餐</div>
                                        <div style="font-size: 12px; color: #666;">2500积分 + 500赠送</div>
                                    </div>
                                    <div style="font-weight: 700; color: #667eea; font-size: 18px;">¥139</div>
                                </div>
                            </div>
                        </div>

                        <button class="btn-primary">💳 立即充值 ¥69</button>
                    </div>

                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div style="font-size: 20px; margin-bottom: 4px;">🏠</div>
                            <div>首页</div>
                        </div>
                        <div class="nav-item">
                            <div style="font-size: 20px; margin-bottom: 4px;">📝</div>
                            <div>记录</div>
                        </div>
                        <div class="nav-item active">
                            <div style="font-size: 20px; margin-bottom: 4px;">💎</div>
                            <div>充值</div>
                        </div>
                        <div class="nav-item">
                            <div style="font-size: 20px; margin-bottom: 4px;">👤</div>
                            <div>我的</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 4. 帮助与支持 -->
        <div class="page-section">
            <div class="section-header">
                <div class="section-title">❓ 帮助与支持</div>
            </div>

            <div class="phone-container">
                <!-- 帮助中心页面 -->
                <div class="phone-frame">
                    <div class="page-label">帮助中心</div>
                    <div class="phone-header">小红书智能体 · 帮助</div>
                    <div class="page-content">
                        <div class="header">
                            <button class="back-btn">← 返回</button>
                            <h1>帮助中心</h1>
                        </div>

                        <div class="card">
                            <h3 style="margin-bottom: 20px; color: #2c3e50;">🔍 常见问题</h3>

                            <div class="list-item">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="font-weight: 500; margin-bottom: 5px;">如何生成高质量文案？</div>
                                        <div style="font-size: 12px; color: #666;">详细填写产品卖点，选择合适的受众群体</div>
                                    </div>
                                    <div style="color: #667eea;">→</div>
                                </div>
                            </div>

                            <div class="list-item">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="font-weight: 500; margin-bottom: 5px;">积分如何获得和使用？</div>
                                        <div style="font-size: 12px; color: #666;">充值获得积分，生成文案消耗积分</div>
                                    </div>
                                    <div style="color: #667eea;">→</div>
                                </div>
                            </div>

                            <div class="list-item">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="font-weight: 500; margin-bottom: 5px;">会员有什么特权？</div>
                                        <div style="font-size: 12px; color: #666;">积分折扣、专属素材、优先支持</div>
                                    </div>
                                    <div style="color: #667eea;">→</div>
                                </div>
                            </div>

                            <div class="list-item">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="font-weight: 500; margin-bottom: 5px;">如何保存和管理素材？</div>
                                        <div style="font-size: 12px; color: #666;">上传素材到私有库，支持分类管理</div>
                                    </div>
                                    <div style="color: #667eea;">→</div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3 style="margin-bottom: 20px; color: #2c3e50;">📖 使用教程</h3>

                            <div class="list-item">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; align-items: center;">
                                        <div style="font-size: 20px; margin-right: 12px;">🎬</div>
                                        <div>
                                            <div style="font-weight: 500;">新手入门指南</div>
                                            <div style="font-size: 12px; color: #666;">5分钟快速上手</div>
                                        </div>
                                    </div>
                                    <div style="color: #667eea;">→</div>
                                </div>
                            </div>

                            <div class="list-item">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; align-items: center;">
                                        <div style="font-size: 20px; margin-right: 12px;">✍️</div>
                                        <div>
                                            <div style="font-weight: 500;">文案生成技巧</div>
                                            <div style="font-size: 12px; color: #666;">提升文案质量的方法</div>
                                        </div>
                                    </div>
                                    <div style="color: #667eea;">→</div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <h3 style="margin-bottom: 20px; color: #2c3e50;">📞 联系我们</h3>

                            <div style="background: rgba(102, 126, 234, 0.05); padding: 20px; border-radius: 12px; text-align: center;">
                                <div style="font-size: 24px; margin-bottom: 10px;">💬</div>
                                <div style="font-weight: 600; margin-bottom: 8px;">技术支持微信</div>
                                <div style="font-size: 14px; color: #667eea; margin-bottom: 15px;">xiaohongshu_ai_support</div>
                                <button class="btn-secondary">复制微信号</button>
                            </div>

                            <div style="margin-top: 20px; background: rgba(255, 107, 107, 0.05); padding: 20px; border-radius: 12px; text-align: center;">
                                <div style="font-size: 24px; margin-bottom: 10px;">📝</div>
                                <div style="font-weight: 600; margin-bottom: 8px;">问题反馈</div>
                                <div style="font-size: 14px; color: #666; margin-bottom: 15px;">遇到问题？告诉我们</div>
                                <button class="btn-secondary">提交反馈</button>
                            </div>
                        </div>
                    </div>

                    <div class="bottom-nav">
                        <div class="nav-item">
                            <div style="font-size: 20px; margin-bottom: 4px;">🏠</div>
                            <div>首页</div>
                        </div>
                        <div class="nav-item">
                            <div style="font-size: 20px; margin-bottom: 4px;">📝</div>
                            <div>记录</div>
                        </div>
                        <div class="nav-item">
                            <div style="font-size: 20px; margin-bottom: 4px;">💎</div>
                            <div>充值</div>
                        </div>
                        <div class="nav-item">
                            <div style="font-size: 20px; margin-bottom: 4px;">👤</div>
                            <div>我的</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 页面结束 -->
        <div style="text-align: center; padding: 60px 20px; color: rgba(255,255,255,0.8);">
            <div style="font-size: 18px; font-weight: 600; margin-bottom: 10px;">🎉 原型展示完成</div>
            <div style="font-size: 14px; opacity: 0.8;">小红书智能体 H5 完整功能原型</div>
            <div style="font-size: 12px; margin-top: 20px; opacity: 0.6;">
                包含登录注册、主页功能、积分充值、个人中心、帮助支持等完整模块
            </div>
        </div>
    </div>
</body>
</html>
